using System;
using System.Numerics;
using ImGuiNET;

namespace CCLineDrawer.UI
{
    /// <summary>
    /// Helper methods for consistent UI styling and behavior
    /// </summary>
    public static class UIHelpers
    {
        // Standard spacing values
        public const float StandardSpacing = 8.0f;
        public const float SectionSpacing = 16.0f;
        public const float TabSpacing = 12.0f;

        // Standard colors
        public static readonly Vector4 SuccessColor = new(0.2f, 0.8f, 0.2f, 1.0f);
        public static readonly Vector4 WarningColor = new(1.0f, 0.8f, 0.2f, 1.0f);
        public static readonly Vector4 ErrorColor = new(0.8f, 0.2f, 0.2f, 1.0f);
        public static readonly Vector4 InfoColor = new(0.2f, 0.6f, 1.0f, 1.0f);

        /// <summary>
        /// Creates a section header with consistent styling
        /// </summary>
        public static void SectionHeader(string text, Vector4? color = null)
        {
            ImGui.Spacing();
            if (color.HasValue)
            {
                ImGui.PushStyleColor(ImGuiCol.Text, color.Value);
            }
            ImGui.TextUnformatted(text);
            if (color.HasValue)
            {
                ImGui.PopStyleColor();
            }
            ImGui.Separator();
            ImGui.Spacing();
        }

        /// <summary>
        /// Creates a help tooltip when hovering over the last item
        /// </summary>
        public static void HelpTooltip(string helpText)
        {
            if (ImGui.IsItemHovered())
            {
                ImGui.BeginTooltip();
                ImGui.PushTextWrapPos(ImGui.GetFontSize() * 35.0f);
                ImGui.TextUnformatted(helpText);
                ImGui.PopTextWrapPos();
                ImGui.EndTooltip();
            }
        }

        /// <summary>
        /// Creates a slider with validation and help text
        /// </summary>
        public static bool ValidatedSliderFloat(string label, ref float value, float min, float max, 
            string format = "%.1f", string helpText = "")
        {
            bool changed = ImGui.SliderFloat(label, ref value, min, max, format);
            
            if (changed)
            {
                value = Math.Clamp(value, min, max);
            }
            
            if (!string.IsNullOrEmpty(helpText))
            {
                HelpTooltip(helpText);
            }
            
            return changed;
        }

        /// <summary>
        /// Creates a color picker with alpha support and validation
        /// </summary>
        public static bool ValidatedColorEdit4(string label, ref Vector4 color, string helpText = "")
        {
            bool changed = ImGui.ColorEdit4(label, ref color, 
                ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar);
            
            if (changed)
            {
                // Ensure alpha is not too low (invisible)
                if (color.W < 0.1f)
                {
                    color.W = 0.1f;
                }
            }
            
            if (!string.IsNullOrEmpty(helpText))
            {
                HelpTooltip(helpText);
            }
            
            return changed;
        }

        /// <summary>
        /// Creates a checkbox with help text
        /// </summary>
        public static bool CheckboxWithHelp(string label, ref bool value, string helpText = "")
        {
            bool changed = ImGui.Checkbox(label, ref value);
            
            if (!string.IsNullOrEmpty(helpText))
            {
                HelpTooltip(helpText);
            }
            
            return changed;
        }

        /// <summary>
        /// Creates a button with consistent styling and optional confirmation
        /// </summary>
        public static bool ConfirmButton(string label, string confirmText = "", Vector4? color = null)
        {
            if (color.HasValue)
            {
                ImGui.PushStyleColor(ImGuiCol.Button, color.Value);
                ImGui.PushStyleColor(ImGuiCol.ButtonHovered, color.Value * 1.1f);
                ImGui.PushStyleColor(ImGuiCol.ButtonActive, color.Value * 0.9f);
            }

            bool clicked = ImGui.Button(label);

            if (color.HasValue)
            {
                ImGui.PopStyleColor(3);
            }

            if (clicked && !string.IsNullOrEmpty(confirmText))
            {
                ImGui.OpenPopup($"Confirm##{label}");
            }

            bool confirmed = false;
            if (ImGui.BeginPopupModal($"Confirm##{label}", ref clicked, ImGuiWindowFlags.AlwaysAutoResize))
            {
                ImGui.TextUnformatted(confirmText);
                ImGui.Spacing();
                
                if (ImGui.Button("Yes"))
                {
                    confirmed = true;
                    ImGui.CloseCurrentPopup();
                }
                
                ImGui.SameLine();
                
                if (ImGui.Button("No"))
                {
                    ImGui.CloseCurrentPopup();
                }
                
                ImGui.EndPopup();
            }

            return string.IsNullOrEmpty(confirmText) ? clicked : confirmed;
        }

        /// <summary>
        /// Creates a status indicator with color and text
        /// </summary>
        public static void StatusIndicator(string label, bool isActive, string activeText = "Active", 
            string inactiveText = "Inactive")
        {
            var color = isActive ? SuccessColor : ErrorColor;
            var text = isActive ? activeText : inactiveText;
            
            ImGui.TextUnformatted($"{label}: ");
            ImGui.SameLine();
            ImGui.TextColored(color, text);
        }

        /// <summary>
        /// Creates a progress bar with text overlay
        /// </summary>
        public static void ProgressBarWithText(float fraction, Vector2 size, string text)
        {
            ImGui.ProgressBar(fraction, size, "");
            
            // Overlay text
            var drawList = ImGui.GetWindowDrawList();
            var pos = ImGui.GetItemRectMin();
            var textSize = ImGui.CalcTextSize(text);
            var textPos = new Vector2(
                pos.X + (size.X - textSize.X) * 0.5f,
                pos.Y + (size.Y - textSize.Y) * 0.5f
            );
            
            drawList.AddText(textPos, ImGui.GetColorU32(ImGuiCol.Text), text);
        }

        /// <summary>
        /// Creates a collapsible section
        /// </summary>
        public static bool CollapsingSection(string label, bool defaultOpen = false)
        {
            ImGui.Spacing();
            bool isOpen = ImGui.CollapsingHeader(label, defaultOpen ? ImGuiTreeNodeFlags.DefaultOpen : ImGuiTreeNodeFlags.None);
            if (isOpen)
            {
                ImGui.Indent();
                ImGui.Spacing();
            }
            return isOpen;
        }

        /// <summary>
        /// Ends a collapsible section
        /// </summary>
        public static void EndCollapsingSection()
        {
            ImGui.Spacing();
            ImGui.Unindent();
        }
    }
}
