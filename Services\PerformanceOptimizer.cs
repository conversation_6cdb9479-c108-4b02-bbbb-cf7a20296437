using System;
using System.Collections.Generic;
using System.Diagnostics;
using Dalamud.Plugin.Services;

namespace CCLineDrawer.Services
{
    /// <summary>
    /// Service for monitoring and optimizing plugin performance
    /// </summary>
    public class PerformanceOptimizer
    {
        private readonly IPluginLog _log;
        private readonly Dictionary<string, PerformanceMetric> _metrics = new();
        private readonly object _lockObject = new();

        public PerformanceOptimizer(IPluginLog log)
        {
            _log = log;
        }

        public IDisposable StartMeasurement(string operationName)
        {
            return new PerformanceMeasurement(this, operationName);
        }

        private void RecordMeasurement(string operationName, long elapsedMs)
        {
            lock (_lockObject)
            {
                if (!_metrics.TryGetValue(operationName, out var metric))
                {
                    metric = new PerformanceMetric(operationName);
                    _metrics[operationName] = metric;
                }

                metric.AddMeasurement(elapsedMs);

                // Log warning if operation is taking too long
                if (elapsedMs > 16) // More than one frame at 60fps
                {
                    _log.Warning($"Slow operation detected: {operationName} took {elapsedMs}ms");
                }
            }
        }

        public void LogPerformanceReport()
        {
            lock (_lockObject)
            {
                _log.Information("=== Performance Report ===");
                foreach (var metric in _metrics.Values)
                {
                    _log.Information($"{metric.OperationName}: Avg={metric.AverageMs:F2}ms, Max={metric.MaxMs}ms, Count={metric.CallCount}");
                }
            }
        }

        private class PerformanceMeasurement : IDisposable
        {
            private readonly PerformanceOptimizer _optimizer;
            private readonly string _operationName;
            private readonly Stopwatch _stopwatch;

            public PerformanceMeasurement(PerformanceOptimizer optimizer, string operationName)
            {
                _optimizer = optimizer;
                _operationName = operationName;
                _stopwatch = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                _stopwatch.Stop();
                _optimizer.RecordMeasurement(_operationName, _stopwatch.ElapsedMilliseconds);
            }
        }

        private class PerformanceMetric
        {
            public string OperationName { get; }
            public long TotalMs { get; private set; }
            public long MaxMs { get; private set; }
            public int CallCount { get; private set; }
            public double AverageMs => CallCount > 0 ? (double)TotalMs / CallCount : 0;

            public PerformanceMetric(string operationName)
            {
                OperationName = operationName;
            }

            public void AddMeasurement(long elapsedMs)
            {
                TotalMs += elapsedMs;
                MaxMs = Math.Max(MaxMs, elapsedMs);
                CallCount++;
            }
        }
    }

    /// <summary>
    /// Configuration for performance settings
    /// </summary>
    public class PerformanceSettings
    {
        public int MaxPlayersToProcess { get; set; } = 50;
        public int CacheUpdateIntervalMs { get; set; } = 100;
        public bool EnablePerformanceLogging { get; set; } = false;
        public int MaxDrawDistance { get; set; } = 50;
        
        // Frame rate limiting
        public bool EnableFrameRateLimiting { get; set; } = true;
        public int TargetFps { get; set; } = 60;
        
        // LOD (Level of Detail) settings
        public bool EnableLOD { get; set; } = true;
        public float HighDetailDistance { get; set; } = 20f;
        public float MediumDetailDistance { get; set; } = 35f;
        
        public void Validate()
        {
            MaxPlayersToProcess = Math.Clamp(MaxPlayersToProcess, 10, 100);
            CacheUpdateIntervalMs = Math.Clamp(CacheUpdateIntervalMs, 50, 1000);
            MaxDrawDistance = Math.Clamp(MaxDrawDistance, 10, 100);
            TargetFps = Math.Clamp(TargetFps, 30, 144);
            HighDetailDistance = Math.Clamp(HighDetailDistance, 5f, 50f);
            MediumDetailDistance = Math.Clamp(MediumDetailDistance, HighDetailDistance, 100f);
        }
    }
}
