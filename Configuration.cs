﻿using Dalamud.Configuration;
using Dalamud.Plugin;
using System;
using System.Collections.Generic;
using System.Numerics;
using Newtonsoft.Json;

namespace CCLineDrawer;

[Serializable]
public enum UiTheme
{
    Default,
    Blue,
    Red,
    Green,
    Purple,
    Custom
}

[Serializable]
public class Configuration : IPluginConfiguration
{
    public int Version { get; set; } = 0;

    public bool IsConfigWindowMovable { get; set; } = true;
    public bool EnableLineDrawing { get; set; } = true;

    // Line drawing settings
    public float LineThickness { get; set; } = 2.0f;
    public uint LineColor { get; set; } = 0xFF00FFFF; // ARGB format (cyan)
    public uint TextColor { get; set; } = 0xFFFFFFFF; // ARGB format (white)
    public bool DrawToAllPlayers { get; set; } = true;
    public bool DrawToEnemiesOnly { get; set; } = false;
    public bool DrawToAlliesOnly { get; set; } = false;
    public float MaxDrawDistance { get; set; } = 50.0f;
    public bool ShowDistance { get; set; } = true;
    public float OverlayTextScale { get; set; } = 0.8f; // Scale factor for in-game overlay text
    public bool ShowDebugWindow { get; set; } = false; // Whether to show the debug window

    // UI Customization settings
    public UiTheme SelectedTheme { get; set; } = UiTheme.Default;
    public uint UiBackgroundColor { get; set; } = 0xF0101010; // ARGB format (dark gray)
    public uint UiTextColor { get; set; } = 0xFFFFFFFF; // ARGB format (white)
    public uint UiHeaderColor { get; set; } = 0xFF00FFFF; // ARGB format (cyan)
    public uint UiAccentColor { get; set; } = 0xFF00FF00; // ARGB format (green)
    public float UiScale { get; set; } = 1.0f;
    public Vector2 MainWindowPosition { get; set; } = new(100, 100);
    public bool LockMainWindowPosition { get; set; } = false;



    // Target filtering settings
    public bool EnableTargetFiltering { get; set; } = true;
    public bool FilterTanks { get; set; } = true;
    public bool FilterHealers { get; set; } = true;
    public bool FilterDPS { get; set; } = true;
    public uint TankColor { get; set; } = 0xFF0000FF; // ABGR format (red)
    public uint HealerColor { get; set; } = 0xFF00FF00; // ABGR format (green)
    public uint DPSColor { get; set; } = 0xFFFF0000; // ABGR format (blue)





    // Shared target settings
    public bool EnableSharedTargetIndicator { get; set; } = true;
    public uint SharedTargetColor { get; set; } = 0xFFFFFF00; // ARGB format (yellow)
    public float SharedTargetLineThickness { get; set; } = 3.0f;
    public bool PulseSharedTargetLine { get; set; } = true;
    public int MinimumShareCount { get; set; } = 2; // Minimum number of party members targeting the same enemy
    public bool ShowShareCountText { get; set; } = true;

    // Kill Potential Indicators settings
    public bool EnableKillPotentialIndicators { get; set; } = true;
    public float KillThreshold { get; set; } = 0.35f; // 35% health
    public uint KillTargetColor { get; set; } = 0xFFFF0000; // ARGB format (bright red)
    public float KillTargetLineThickness { get; set; } = 3.5f;
    public bool PulseKillTargetLine { get; set; } = true;
    public bool ShowKillIcon { get; set; } = true;
    public string KillIconSymbol { get; set; } = "×"; // Cross symbol
    public float KillIconScale { get; set; } = 2.0f;

    // Enemy Targeting Me settings (kept for compatibility)
    public bool EnableEnemyTargetingMe { get; set; } = true;
    public uint EnemyTargetingMeColor { get; set; } = 0xFFFF00FF; // ARGB format (magenta)
    public float EnemyTargetingMeLineThickness { get; set; } = 4.0f;
    public bool PulseEnemyTargetingMeLine { get; set; } = true;
    public bool ShowEnemyTargetingMeIcon { get; set; } = true;
    public string EnemyTargetingMeIconSymbol { get; set; } = "!"; // Exclamation mark
    public float EnemyTargetingMeIconScale { get; set; } = 2.0f;
    public bool ShowEnemyTargetingMeNames { get; set; } = true; // Show names of enemies targeting you
    public bool ShowEnemyTargetingMeList { get; set; } = true; // Show list of enemies targeting you on screen

    // Focus Target settings
    public bool EnableFocusTargetTracking { get; set; } = true;
    public uint FocusTargetColor { get; set; } = 0xFFFF8000; // ARGB format (orange)
    public float FocusTargetLineThickness { get; set; } = 4.0f;
    public bool PulseFocusTargetLine { get; set; } = true;
    public bool ShowFocusTargetIcon { get; set; } = true;
    public string FocusTargetIconSymbol { get; set; } = "★"; // Star symbol
    public float FocusTargetIconScale { get; set; } = 2.0f;



    // Additional settings can be added here if needed



    // the below exist just to make saving less cumbersome
    public void Save()
    {
        Plugin.PluginInterface.SavePluginConfig(this);
    }



    // Apply theme colors based on the selected theme
    public void ApplyThemeColors()
    {
        // Log the current theme being applied
        if (Plugin.Log != null)
        {
            Plugin.Log.Information($"Applying theme: {SelectedTheme}");
        }

        // ImGui uses ABGR format for uint colors
        // We'll use direct uint values to ensure correct color representation
        switch (SelectedTheme)
        {

            case UiTheme.Blue:
                // Blue theme - dark blue background, white text, cyan headers, light blue accents
                UiBackgroundColor = 0xF0301000;  // ABGR: Alpha=0.94, Blue=48, Green=16, Red=0 (dark blue)
                UiTextColor = 0xFFFFFFFF;        // ABGR: Alpha=1.0, Blue=255, Green=255, Red=255
                UiHeaderColor = 0xFFFFFF00;       // ABGR: Alpha=1.0, Blue=255, Green=255, Red=0 (cyan)
                UiAccentColor = 0xFFFF8000;       // ABGR: Alpha=1.0, Blue=255, Green=128, Red=0 (light blue)
                break;

            case UiTheme.Red:
                // Red theme - dark red background, white text, light red headers, red accents
                UiBackgroundColor = 0xF0100030;  // ABGR: Alpha=0.94, Blue=16, Green=0, Red=48 (dark red)
                UiTextColor = 0xFFFFFFFF;        // ABGR: Alpha=1.0, Blue=255, Green=255, Red=255
                UiHeaderColor = 0xFF8080FF;       // ABGR: Alpha=1.0, Blue=128, Green=128, Red=255 (light red)
                UiAccentColor = 0xFF0000FF;       // ABGR: Alpha=1.0, Blue=0, Green=0, Red=255 (red)
                break;

            case UiTheme.Green:
                // Green theme - dark green background, white text, light green headers, green accents
                UiBackgroundColor = 0xF0003010;  // ABGR: Alpha=0.94, Blue=0, Green=48, Red=16 (dark green)
                UiTextColor = 0xFFFFFFFF;        // ABGR: Alpha=1.0, Blue=255, Green=255, Red=255
                UiHeaderColor = 0xFF80FF80;       // ABGR: Alpha=1.0, Blue=128, Green=255, Red=128 (light green)
                UiAccentColor = 0xFF00FF00;       // ABGR: Alpha=1.0, Blue=0, Green=255, Red=0 (green)
                break;

            case UiTheme.Purple:
                // Purple theme - dark purple background, white text, light purple headers, magenta accents
                UiBackgroundColor = 0xF0300030;  // ABGR: Alpha=0.94, Blue=48, Green=0, Red=48 (dark purple)
                UiTextColor = 0xFFFFFFFF;        // ABGR: Alpha=1.0, Blue=255, Green=255, Red=255
                UiHeaderColor = 0xFFFF80FF;       // ABGR: Alpha=1.0, Blue=255, Green=128, Red=255 (light purple)
                UiAccentColor = 0xFFFF00FF;       // ABGR: Alpha=1.0, Blue=255, Green=0, Red=255 (magenta)
                break;

            case UiTheme.Default:
            default:
                // Default theme (dark with cyan accents)
                UiBackgroundColor = 0xF0101010;  // ABGR: Alpha=0.94, Blue=16, Green=16, Red=16
                UiTextColor = 0xFFFFFFFF;        // ABGR: Alpha=1.0, Blue=255, Green=255, Red=255
                UiHeaderColor = 0xFFFFFF00;       // ABGR: Alpha=1.0, Blue=255, Green=255, Red=0 (cyan)
                UiAccentColor = 0xFF00FF00;       // ABGR: Alpha=1.0, Blue=0, Green=255, Red=0 (green)
                break;
        }

        // Log the applied colors
        if (Plugin.Log != null)
        {
            Plugin.Log.Information($"Theme colors applied - BG: 0x{UiBackgroundColor:X8}, Text: 0x{UiTextColor:X8}, Header: 0x{UiHeaderColor:X8}, Accent: 0x{UiAccentColor:X8}");
        }

        Save();
    }

    // Helper method to convert RGB values to ImGui's uint color format (ABGR)
    public uint ImGuiColorToUint(byte r, byte g, byte b, byte a)
    {
        // ImGui uses ABGR format (alpha in most significant bits, then blue, green, red)
        uint color = (uint)((a << 24) | (b << 16) | (g << 8) | r);

        // Log the color conversion for debugging
        if (Plugin.Log != null)
        {
            Plugin.Log.Debug($"Color conversion: R:{r} G:{g} B:{b} A:{a} -> 0x{color:X8}");
        }

        return color;
        }
    }
