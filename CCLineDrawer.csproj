<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Authors>HiAmMilkWIthToast</Authors>
    <Version>*******</Version>
    <Description>A plugin that draws lines to other players in Crystalline Conflict to help with positioning and targeting.</Description>
    <PackageLicenseExpression>AGPL-3.0-or-later</PackageLicenseExpression>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Platforms>x64</Platforms>
    <LangVersion>latest</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <NoWarn>CS1591;CS8632;CS8600;CS8602;CS8603</NoWarn>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <DalamudLibPath>$(appdata)\XIVLauncher\addon\Hooks\dev\</DalamudLibPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DalamudPackager" Version="12.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="FFXIVClientStructs">
      <HintPath>$(DalamudLibPath)FFXIVClientStructs.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="InteropGenerator.Runtime">
      <HintPath>$(DalamudLibPath)InteropGenerator.Runtime.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>$(DalamudLibPath)Newtonsoft.Json.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Dalamud">
      <HintPath>$(DalamudLibPath)Dalamud.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="ImGui.NET">
      <HintPath>$(DalamudLibPath)ImGui.NET.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="ImGuiScene">
      <HintPath>$(DalamudLibPath)ImGuiScene.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Lumina">
      <HintPath>$(DalamudLibPath)Lumina.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Lumina.Excel">
      <HintPath>$(DalamudLibPath)Lumina.Excel.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>
</Project>
