using Dalamud.Game.Command;
using Dalamud.IoC;
using Dalamud.Plugin;
using Dalamud.Interface.Windowing;
using Dalamud.Plugin.Services;
using CCLineDrawer.Windows;
using Dalamud.Game.ClientState.Objects;
using Dalamud.Game.ClientState.Objects.Types;
using Dalamud.Game.ClientState.Objects.Enums;
using Dalamud.Game.ClientState.Objects.SubKinds;
using System.Numerics;
using ImGuiNET;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Diagnostics;
using FFXIVClientStructs.FFXIV.Client.Game.Character;

namespace CCLineDrawer;

public sealed class Plugin : IDalamudPlugin
{
    // Dalamud services
    [PluginService] internal static IDalamudPluginInterface PluginInterface { get; private set; } = null!;
    [PluginService] internal static ICommandManager CommandManager { get; private set; } = null!;
    [PluginService] internal static IClientState ClientState { get; private set; } = null!;
    [PluginService] internal static IDataManager DataManager { get; private set; } = null!;
    [PluginService] internal static IPluginLog Log { get; private set; } = null!;
    [PluginService] internal static IObjectTable ObjectTable { get; private set; } = null!;
    [PluginService] internal static IGameGui GameGui { get; private set; } = null!;
    [PluginService] internal static ICondition Condition { get; private set; } = null!;
    [PluginService] internal static ITargetManager TargetManager { get; private set; } = null!;
    [PluginService] internal static IFramework Framework { get; private set; } = null!;
    [PluginService] internal static IGameInteropProvider GameInteropProvider { get; private set; } = null!;

    // Plugin constants
    private const string CommandName = "/ccline";
    private const string PluginName = "Crystalline Conflict Line Drawer";
    private const int PLAYER_CACHE_UPDATE_INTERVAL_MS = 100; // Update player cache every 100ms

    // Plugin state
    public Configuration Configuration { get; init; }
    public readonly WindowSystem WindowSystem = new("CCLineDrawer");
    private ConfigWindow ConfigWindow { get; init; }
    private MainWindow MainWindow { get; init; }


    // Performance tracking
    private readonly Stopwatch _drawPerformanceTimer = new();

    // Caches for performance optimization
    private readonly List<ICharacter> _playerCache = new(40);
    private readonly List<ICharacter> _partyMemberCache = new(8);
    private DateTime _lastPlayerCacheUpdate = DateTime.MinValue;
    private DateTime _lastPartyMemberCacheUpdate = DateTime.MinValue;
    private const int PARTY_CACHE_UPDATE_INTERVAL_MS = 500; // Update party cache every 500ms

    // Shared target tracking
    private readonly Dictionary<IntPtr, SharedTargetInfo> _sharedTargets = [];

    /// <summary>
    /// Plugin constructor - initializes the plugin and sets up event handlers
    /// </summary>
    public Plugin()
    {
        // Load or create configuration
        Configuration = PluginInterface.GetPluginConfig() as Configuration ?? new Configuration();
        Configuration.ApplyThemeColors();
        Configuration.Save();



        // Initialize UI windows
        ConfigWindow = new ConfigWindow(this);
        MainWindow = new MainWindow(this);

        WindowSystem.AddWindow(ConfigWindow);
        WindowSystem.AddWindow(MainWindow);

        // Register command handler
        CommandManager.AddHandler(CommandName, new CommandInfo(OnCommand)
        {
            HelpMessage = $"Open the {PluginName} configuration window."
        });

        // Register event handlers
        PluginInterface.UiBuilder.Draw += DrawUI;
        PluginInterface.UiBuilder.Draw += DrawLines;
        PluginInterface.UiBuilder.OpenConfigUi += ToggleConfigUI;
        PluginInterface.UiBuilder.OpenMainUi += ToggleMainUI;

        Log.Information($"=== {PluginName} plugin loaded ===");
    }

    // SharedTargetInfo class for tracking targets
    private class SharedTargetInfo(ICharacter target)
    {
        public ICharacter Target { get; private set; } = target;
        public uint LastHP { get; private set; } = target.CurrentHp;
        public DateTime LastHPChangeTime { get; private set; } = DateTime.Now;
        public bool IsBeingAttacked { get; private set; } = false;
        public int EstimatedAttackers { get; private set; } = 1; // Start with 1 (yourself)
        public List<ICharacter> TargetingPlayers { get; } = [];
        public Dictionary<string, DateTime> NearbyPlayers { get; } = [];

        public void UpdateHealthTracking()
        {
            if (Target == null) return;

            // Check if HP has changed
            uint currentHP = Target.CurrentHp;
            if (currentHP != LastHP)
            {
                // HP has changed, update tracking
                LastHPChangeTime = DateTime.Now;
                IsBeingAttacked = currentHP < LastHP; // Only consider it an attack if HP decreased

                // If HP decreased, estimate the number of attackers based on nearby players
                if (IsBeingAttacked)
                {
                    // Start with a base of 1 (the local player)
                    EstimatedAttackers = 1;

                    // Add nearby players who might be attacking
                    // We'll use a simple heuristic: count players who have been nearby recently
                    int nearbyPlayerCount = 0;
                    DateTime cutoffTime = DateTime.Now.AddSeconds(-5); // Only count players seen in the last 5 seconds

                    foreach (var entry in NearbyPlayers)
                    {
                        if (entry.Value > cutoffTime)
                        {
                            nearbyPlayerCount++;
                        }
                    }

                    // Limit the estimated attackers to a reasonable number
                    EstimatedAttackers = Math.Min(nearbyPlayerCount, 8);
                    if (EstimatedAttackers < 1) EstimatedAttackers = 1; // Always at least 1 attacker
                }

                // Update the last HP
                LastHP = currentHP;
            }
            else
            {
                // If it's been more than 5 seconds since the last HP change, consider it not being attacked
                if ((DateTime.Now - LastHPChangeTime).TotalSeconds > 5)
                {
                    IsBeingAttacked = false;
                }
            }
        }

        public void AddNearbyPlayer(string playerName)
        {
            if (string.IsNullOrEmpty(playerName)) return;

            // Update the timestamp for this player
            NearbyPlayers[playerName] = DateTime.Now;

            // Clean up old entries (players who haven't been seen in a while)
            DateTime cutoffTime = DateTime.Now.AddSeconds(-30); // Remove players not seen in 30 seconds
            var keysToRemove = NearbyPlayers.Where(kvp => kvp.Value < cutoffTime).Select(kvp => kvp.Key).ToList();
            foreach (var key in keysToRemove)
            {
                NearbyPlayers.Remove(key);
            }
        }

        // Get a list of nearby players who might be targeting this enemy
        public List<string> GetNearbyPlayerNames()
        {
            // Return a distinct list of player names to avoid duplicates
            return [.. NearbyPlayers.Keys];
        }
    }

    // Method to update shared targets information
    // Tracks the local player's target and monitors health changes to estimate if others are attacking
    private void UpdateSharedTargets()
    {
        try
        {
            // Get the local player
            var localPlayer = ClientState.LocalPlayer;
            if (localPlayer == null) return;

            // Get the local player's current target
            var currentTarget = TargetManager.Target as ICharacter;

            // First, update existing targets (to track health changes)
            List<IntPtr>? targetsToRemove = null;
            foreach (var entry in _sharedTargets)
            {
                // Check if the target still exists
                bool targetExists = false;
                foreach (var obj in ObjectTable)
                {
                    if (obj.Address == entry.Key && obj is ICharacter)
                    {
                        targetExists = true;
                        break;
                    }
                }

                if (!targetExists)
                {
                    // Target no longer exists, mark for removal
                    targetsToRemove ??= [];
                    targetsToRemove.Add(entry.Key);
                    continue;
                }

                // Update health tracking for this target
                entry.Value.UpdateHealthTracking();

                // If this is no longer the current target and it's been more than 5 seconds, remove it
                if (currentTarget == null || entry.Key != currentTarget.Address)
                {
                    if ((DateTime.Now - entry.Value.LastHPChangeTime).TotalSeconds > 5)
                    {
                        targetsToRemove ??= [];
                        targetsToRemove.Add(entry.Key);
                    }
                }
            }

            // Remove targets that no longer exist
            if (targetsToRemove != null)
            {
                foreach (var address in targetsToRemove)
                {
                    _sharedTargets.Remove(address);
                }
            }

            // Update party member cache only periodically to reduce CPU usage
            bool shouldUpdatePartyCache = (DateTime.Now - _lastPartyMemberCacheUpdate).TotalMilliseconds >= PARTY_CACHE_UPDATE_INTERVAL_MS;

            if (shouldUpdatePartyCache)
            {
                _partyMemberCache.Clear();

                // Get all party members without LINQ
                foreach (var obj in ObjectTable)
                {
                    if (obj.ObjectKind == ObjectKind.Player && obj is ICharacter character)
                    {
                        _partyMemberCache.Add(character);
                    }
                }

                _lastPartyMemberCacheUpdate = DateTime.Now;
            }

            // If we have a current target, make sure it's in the dictionary
            if (currentTarget != null && !currentTarget.StatusFlags.HasFlag(StatusFlags.PartyMember))
            {
                if (!_sharedTargets.TryGetValue(currentTarget.Address, out var targetInfo))
                {
                    // Create a new entry for this target
                    targetInfo = new SharedTargetInfo(currentTarget);
                    _sharedTargets[currentTarget.Address] = targetInfo;
                }

                // Add the local player to the list of targeting players
                if (!targetInfo.TargetingPlayers.Contains(localPlayer))
                {
                    targetInfo.TargetingPlayers.Add(localPlayer);
                }

                // Add the local player's name to the nearby players list
                targetInfo.AddNearbyPlayer(localPlayer.Name.TextValue);

                // Find nearby party members who might be targeting this enemy
                foreach (var partyMember in _partyMemberCache)
                {
                    // Skip if it's the local player (already added)
                    if (partyMember.Address == localPlayer.Address) continue;

                    // Skip if it's not a party member
                    if (!partyMember.StatusFlags.HasFlag(StatusFlags.PartyMember)) continue;

                    // Calculate distance to the target
                    float distance = Vector3.Distance(partyMember.Position, currentTarget.Position);

                    // Check if the party member is facing the target (rough line-of-sight check)
                    bool isFacingTarget = IsCharacterFacingTarget(partyMember, currentTarget);

                    // Adjust distance threshold based on role (ranged players can attack from further away)
                    float distanceThreshold = 15.0f; // Default threshold
                    var role = GetPlayerRole(partyMember);
                    if (role == PlayerRole.DPS)
                    {
                        // Ranged DPS can attack from further away
                        distanceThreshold = 25.0f;
                    }
                    else if (role == PlayerRole.Healer)
                    {
                        // Healers are usually further back
                        distanceThreshold = 20.0f;
                    }

                    // If the party member is close to the target and facing it, they might be targeting it
                    if (distance < distanceThreshold && isFacingTarget)
                    {
                        targetInfo.AddNearbyPlayer(partyMember.Name.TextValue);
                    }
                    // If they're very close, they might be targeting it even if not directly facing it (melee)
                    else if (distance < 5.0f)
                    {
                        targetInfo.AddNearbyPlayer(partyMember.Name.TextValue);
                    }
                }

                // Update health tracking
                targetInfo.UpdateHealthTracking();

                Log.Debug($"Current target: {currentTarget.Name.TextValue} is being tracked as a shared target");
                Log.Debug($"Estimated attackers: {targetInfo.EstimatedAttackers}, Is being attacked: {targetInfo.IsBeingAttacked}");
                Log.Debug($"Nearby players: {string.Join(", ", targetInfo.GetNearbyPlayerNames())}");
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error in UpdateSharedTargets");
        }
    }

    // Helper method to determine a player's role based on their job
    public static PlayerRole GetPlayerRole(ICharacter player)
    {
        try
        {
            return DeterminePlayerRole(player);
        }
        catch
        {
            // If we can't determine the role, default to DPS
            return PlayerRole.DPS;
        }
    }

    // Helper method to get a player's job icon
    public static string GetPlayerJobIcon(ICharacter player)
    {
        try
        {
            // First check if we have the job ID cached
            if (player == null) return "❓"; // Handle null player

            if (_playerJobCache.TryGetValue(player.Address, out uint jobId))
            {
                return GetJobIcon(jobId);
            }

            // If not cached, determine the role and return a generic icon based on role
            var role = GetPlayerRole(player);
            return role switch
            {
                PlayerRole.Tank => "🛡️",
                PlayerRole.Healer => "💚",
                PlayerRole.DPS => "🗡️",
                PlayerRole.DoH => "🔨",
                PlayerRole.DoL => "🌿",
                _ => "❓"
            };
        }
        catch (Exception ex)
        {
            Log.Error($"Error getting player job icon: {ex.Message}");
            return "❓"; // Default to unknown if there's an error
        }
    }

    // Public method to check if in PvP zone (for UI)
    public static bool IsInPvPZonePublic()
    {
        return IsInPvPZone();
    }

    // Public method to check if enemy players are nearby (for UI)
    public static bool HasEnemyPlayersNearbyPublic()
    {
        return HasEnemyPlayersNearby();
    }

    // Check if we're in a PvP zone or if PvP is possible in the current area
    private static bool IsInPvPZone()
    {
        try
        {
            // Get the current territory type
            var territoryType = ClientState.TerritoryType;

            // Create a HashSet with all known PvP territory IDs
            var pvpTerritoryIds = new HashSet<ushort>
            {
                // Crystalline Conflict arenas
                1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131,
                // Frontline maps
                376, 431, 554, 888,
                353, 354, 355, 356, 357, 358, 378, 379, 422, 423, 424, 425, 426, 427, 428, 429, 430,
                // The Feast / Rival Wings maps
                791, 1065,
                689, 690, 691, 692, 693, 794, 795, 796, 797, 798,
                // PvP preparation areas
                250, 1031, 1032, 1033,
                // PvP training areas
                1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020,
                // Any other PvP-related zones
                1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050
            };

            // First check if we're in a known PvP territory
            var isInPvPZone = pvpTerritoryIds.Contains(territoryType);

            // If we're not in a recognized PvP zone, check if there are any enemy players nearby
            if (!isInPvPZone && ClientState.LocalPlayer != null && HasEnemyPlayersNearby())
            {
                Log.Debug("PvP detected based on enemy players nearby");
                return true;
            }

            return isInPvPZone;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error checking if in PvP zone");
            return false;
        }
    }

    // Check if there are any enemy players nearby (players not in your party)
    private static bool HasEnemyPlayersNearby()
    {
        try
        {
            var localPlayer = ClientState.LocalPlayer;
            if (localPlayer == null) return false;

            // Look for any players that are not in your party
            foreach (var obj in ObjectTable)
            {
                if (obj.ObjectKind == ObjectKind.Player &&
                    obj.Address != localPlayer.Address &&
                    obj is ICharacter character &&
                    !character.StatusFlags.HasFlag(StatusFlags.PartyMember) &&
                    character.CurrentHp > 0)
                {
                    // Found at least one living enemy player
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error checking for enemy players nearby");
            return false;
        }
    }

    // Helper method to get a player's current target
    private static ICharacter? GetPlayerTarget(ICharacter player)
    {
        try
        {
            // For the local player, we can use the TargetManager
            if (player.Address == ClientState.LocalPlayer?.Address)
            {
                return TargetManager.Target as ICharacter;
            }

            // For other players, we can't directly access their targets
            return null;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error getting player target");
            return null;
        }
    }



    // Helper method to check if a player is the focus target
    private static bool IsFocusTarget(ICharacter player)
    {
        try
        {
            // Check if the player is the focus target
            return TargetManager.FocusTarget != null && TargetManager.FocusTarget.Address == player.Address;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error checking focus target");
            return false;
        }
    }



    // Check if a player is a shared target
    // Uses a more accurate method to determine who is targeting this enemy
    private bool IsSharedTarget(ICharacter player, out int shareCount, out List<string> playerNames)
    {
        shareCount = 0; // Start with 0 and count actual targeting players
        playerNames = []; // Initialize empty list

        if (!Configuration.EnableSharedTargetIndicator) return false;

        // Get the local player
        var localPlayer = ClientState.LocalPlayer;
        if (localPlayer == null) return false;

        // Check if the local player is targeting this enemy
        bool isLocalPlayerTarget = false;
        if (TargetManager.Target != null && TargetManager.Target.Address == player.Address)
        {
            isLocalPlayerTarget = true;
            shareCount++;
            playerNames.Add(localPlayer.Name.TextValue);
        }

        // If this is not the local player's target, don't show shared target info
        if (!isLocalPlayerTarget) return false;

        // Use the party member cache instead of querying ObjectTable again
        foreach (var member in _partyMemberCache)
        {
            // Skip if it's not a party member or if it's the local player
            if (!member.StatusFlags.HasFlag(StatusFlags.PartyMember) || member.Address == localPlayer.Address)
                continue;

            // Check if this party member is facing the target and close enough
            if (IsCharacterFacingTarget(member, player))
            {
                // Calculate distance to the target
                float distance = Vector3.Distance(member.Position, player.Position);

                // Adjust distance threshold based on role (ranged players can attack from further away)
                float distanceThreshold = 15.0f; // Default threshold
                var role = GetPlayerRole(member);
                if (role == PlayerRole.DPS)
                {
                    // Ranged DPS can attack from further away
                    distanceThreshold = 25.0f;
                }
                else if (role == PlayerRole.Healer)
                {
                    // Healers are usually further back
                    distanceThreshold = 20.0f;
                }

                // If they're close enough and facing the target, they're likely targeting it
                if (distance < distanceThreshold)
                {
                    shareCount++;
                    playerNames.Add(member.Name.TextValue);
                }
                // If they're very close, they might be targeting it even if not directly facing it (melee)
                else if (distance < 5.0f)
                {
                    shareCount++;
                    playerNames.Add(member.Name.TextValue);
                }
            }
        }

        // Check if this player is in our shared targets dictionary for health tracking
        if (_sharedTargets.TryGetValue(player.Address, out var targetInfo))
        {
            // Update the targeting players list in the shared target info
            targetInfo.TargetingPlayers.Clear();
            targetInfo.TargetingPlayers.Add(localPlayer); // Add local player

            // Add other party members who are targeting this enemy
            foreach (var partyMember in _partyMemberCache)
            {
                // Skip if it's not a party member or if it's the local player
                if (!partyMember.StatusFlags.HasFlag(StatusFlags.PartyMember) || partyMember.Address == localPlayer.Address)
                    continue;

                if (playerNames.Contains(partyMember.Name.TextValue) && !targetInfo.TargetingPlayers.Contains(partyMember))
                {
                    targetInfo.TargetingPlayers.Add(partyMember);
                }
            }

            // Update health tracking
            targetInfo.UpdateHealthTracking();
        }

        // Return true if more than one player is targeting this enemy
        return shareCount > 1;
    }

    // Check if a character is facing another character (within a reasonable angle)
    private static bool IsCharacterFacingTarget(ICharacter source, ICharacter target)
    {
        try
        {
            // Calculate direction from source to target
            Vector3 directionToTarget = Vector3.Normalize(target.Position - source.Position);

            // Get the forward direction of the source character
            float angle = source.Rotation;
            Vector3 forwardDirection = new((float)Math.Sin(angle), 0, (float)Math.Cos(angle));

            // Calculate the dot product between the forward direction and direction to target
            float dotProduct = Vector3.Dot(forwardDirection, directionToTarget);

            // Consider the character to be facing the target if the angle is within 45 degrees
            // cos(45°) ≈ 0.7071, so if dotProduct > 0.7, the angle is less than 45 degrees
            return dotProduct > 0.7f;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error checking if character is facing target");
            return false;
        }
    }



    // This section has been moved to the top of the class

    private void DrawLines()
    {
        try
        {
            // For testing, we'll draw regardless of territory
            // In production, uncomment this: if (!IsInCrystallineConflict) return;

            var localPlayer = ClientState.LocalPlayer;
            if (localPlayer == null) return;

            // Update shared targets information
            if (Configuration.EnableSharedTargetIndicator)
            {
                // Ensure we're on the main thread for ObjectTable operations
                // This is already the case since DrawLines is called from UiBuilder.Draw
                UpdateSharedTargets();
            }



            // Draw lines if enabled
            if (!Configuration.EnableLineDrawing) return;

            // Update player cache only periodically to reduce CPU usage
            bool shouldUpdateCache = (DateTime.Now - _lastPlayerCacheUpdate).TotalMilliseconds >= PLAYER_CACHE_UPDATE_INTERVAL_MS;

            if (shouldUpdateCache)
            {
                _playerCache.Clear();

                // Get all players in the area
                foreach (var obj in ObjectTable)
                {
                    if (obj.ObjectKind == ObjectKind.Player &&
                        obj.Address != localPlayer.Address &&
                        obj is ICharacter character &&
                        character.CurrentHp > 0)
                    {
                        // Apply filters based on configuration
                        bool shouldInclude = false;
                        if (Configuration.DrawToAllPlayers)
                            shouldInclude = true;
                        else if (Configuration.DrawToEnemiesOnly && !character.StatusFlags.HasFlag(StatusFlags.PartyMember))
                            shouldInclude = true;
                        else if (Configuration.DrawToAlliesOnly && character.StatusFlags.HasFlag(StatusFlags.PartyMember))
                            shouldInclude = true;

                        if (shouldInclude)
                            _playerCache.Add(character);
                    }
                }

                _lastPlayerCacheUpdate = DateTime.Now;
            }

            // Get the ImGui draw list for the overlay
            var drawList = ImGui.GetBackgroundDrawList();

            foreach (var player in _playerCache)
            {
                try
                {
                    // Calculate distance
                    var distance = Vector3.Distance(localPlayer.Position, player.Position);
                    if (distance > Configuration.MaxDrawDistance) continue;

                    // Get screen positions
                    if (!GameGui.WorldToScreen(player.Position, out var playerScreenPos)) continue;
                    if (!GameGui.WorldToScreen(localPlayer.Position, out var localPlayerScreenPos)) continue;

                    // Get player health percentage for various features
                    var healthPercentage = GetPlayerHealthPercentage(player);
                    var isKillTarget = false;

                    // Determine player role (simplified for demonstration)
                    var role = DeterminePlayerRole(player);

                    // Log the player name and role for debugging
                    Log.Debug($"Player {player.Name.TextValue} assigned role: {role}");

                    // Apply target filtering if enabled
                    if (Configuration.EnableTargetFiltering)
                    {
                        // Skip players whose roles are filtered out
                        if (role == PlayerRole.Tank && !Configuration.FilterTanks) continue;
                        if (role == PlayerRole.Healer && !Configuration.FilterHealers) continue;
                        if (role == PlayerRole.DPS && !Configuration.FilterDPS) continue;
                    }

                    // Determine line color based on settings
                    uint lineColor = Configuration.LineColor;
                    float lineThickness = Configuration.LineThickness;

                    // Check if this is a shared target (multiple party members targeting the same enemy)
                    bool isSharedTarget = IsSharedTarget(player, out int shareCount, out List<string> targetingPlayerNames);

                    // Check if this is the focus target
                    bool isFocusTarget = IsFocusTarget(player);

                    // Removed check for enemy targeting local player

                    // Removed check for invisible ninja

                    // Apply focus target highlighting if enabled (highest priority)
                    if (Configuration.EnableFocusTargetTracking && isFocusTarget)
                    {
                        lineColor = Configuration.FocusTargetColor;
                        lineThickness = Configuration.FocusTargetLineThickness;

                        // Apply pulse effect if enabled
                        if (Configuration.PulseFocusTargetLine)
                        {
                            // Pulse effect for focus targets
                            float pulseIntensity = (float)Math.Sin(DateTime.Now.Millisecond / 200.0f) * 0.5f + 0.7f;
                            lineThickness *= pulseIntensity;
                        }

                        Log.Debug($"FOCUS TARGET HIGHLIGHTED: {player.Name.TextValue}");
                    }
                    // Apply shared target highlighting if enabled and not a focus target
                    else if (Configuration.EnableSharedTargetIndicator && isSharedTarget && !isKillTarget)
                    {
                        lineColor = Configuration.SharedTargetColor;
                        lineThickness = Configuration.SharedTargetLineThickness;

                        // Apply pulse effect if enabled
                        if (Configuration.PulseSharedTargetLine)
                        {
                            // Pulse effect for shared targets
                            float pulseIntensity = (float)Math.Sin(DateTime.Now.Millisecond / 250.0f) * 0.4f + 0.6f;
                            lineThickness *= pulseIntensity;
                        }
                    }
                    // Apply role-based coloring if enabled and not a shared target or kill target
                    else if (Configuration.EnableTargetFiltering && !isSharedTarget && !isKillTarget)
                    {
                        // Set color based on role
                        switch (role)
                        {
                            case PlayerRole.Tank:
                                lineColor = Configuration.TankColor;
                                Log.Debug($"Assigned TANK color (red - 0x{Configuration.TankColor:X8}) to {player.Name.TextValue}");
                                break;
                            case PlayerRole.Healer:
                                lineColor = Configuration.HealerColor;
                                Log.Debug($"Assigned HEALER color (green - 0x{Configuration.HealerColor:X8}) to {player.Name.TextValue}");
                                break;
                            case PlayerRole.DPS:
                                lineColor = Configuration.DPSColor;
                                Log.Debug($"Assigned DPS color (blue - 0x{Configuration.DPSColor:X8}) to {player.Name.TextValue}");
                                break;
                            case PlayerRole.DoH:
                                lineColor = 0xFFFF00FF; // Purple for crafters (ABGR format)
                                Log.Debug($"Assigned DoH color (purple - 0xFFFF00FF) to {player.Name.TextValue}");
                                break;
                            case PlayerRole.DoL:
                                lineColor = 0xFFFFFF00; // Yellow for gatherers (ABGR format)
                                Log.Debug($"Assigned DoL color (yellow - 0xFFFFFF00) to {player.Name.TextValue}");
                                break;
                        }
                    }



                    // Check if this is a kill potential target - HIGHEST PRIORITY
                    // This check needs to happen before any other visual effects
                    if (Configuration.EnableKillPotentialIndicators)
                    {
                        // Log detailed information about the health check
                        Log.Debug($"Kill check for {player.Name.TextValue}: Health {healthPercentage:F2} ({(int)(healthPercentage * 100)}%), Threshold {Configuration.KillThreshold:F2} ({(int)(Configuration.KillThreshold * 100)}%)");

                        // Check if health is below the kill threshold and player is not dead (health > 0)
                        if (healthPercentage < Configuration.KillThreshold && healthPercentage > 0)
                        {
                            // Kill potential has highest priority
                            isKillTarget = true;
                            lineColor = Configuration.KillTargetColor;
                            lineThickness = Configuration.KillTargetLineThickness;

                            Log.Debug($"KILL TARGET IDENTIFIED: {player.Name.TextValue} with {(int)(healthPercentage * 100)}% health");

                            // Apply pulse effect if enabled
                            if (Configuration.PulseKillTargetLine)
                            {
                                // More pronounced pulse effect for kill targets
                                float pulseIntensity = (float)Math.Sin(DateTime.Now.Millisecond / 150.0f) * 0.6f + 0.7f;
                                lineThickness *= pulseIntensity;
                            }
                        }
                    }


                    // Draw the line with the determined color and thickness
                    drawList.AddLine(
                        new Vector2(localPlayerScreenPos.X, localPlayerScreenPos.Y),
                        new Vector2(playerScreenPos.X, playerScreenPos.Y),
                        lineColor,
                        lineThickness
                    );

                    // Get job icon for the player
                    string jobIcon = GetPlayerJobIcon(player);

                    // Draw player name and distance at the end of the line
                    string displayText = Configuration.ShowDistance
                        ? $"{jobIcon} {player.Name.TextValue} ({distance:F1} yalms)"
                        : $"{jobIcon} {player.Name.TextValue}";

                    // Add focus target icon if this is the focus target and the option is enabled
                    if (Configuration.EnableFocusTargetTracking && Configuration.ShowFocusTargetIcon && isFocusTarget)
                    {
                        displayText = $"{Configuration.FocusTargetIconSymbol} {displayText}";
                    }

                    // Add kill icon for kill potential targets if enabled
                    if (isKillTarget && Configuration.ShowKillIcon)
                    {
                        // Draw the kill icon (cross symbol) next to the player name
                        float iconScale = Configuration.KillIconScale;
                        ImGui.PushFont(ImGui.GetFont());
                        ImGui.SetWindowFontScale(iconScale);
                        var iconSize = ImGui.CalcTextSize(Configuration.KillIconSymbol);
                        ImGui.PopFont();

                        // Draw the icon with a slight offset from the player position
                        drawList.AddText(
                            new Vector2(playerScreenPos.X - iconSize.X / 2, playerScreenPos.Y - iconSize.Y / 2),
                            Configuration.KillTargetColor,
                            Configuration.KillIconSymbol
                        );

                        // Add "KILL!" text above the player with a black outline for better visibility
                        string killText = "KILL!";
                        Vector2 textPos = new(playerScreenPos.X - 15, playerScreenPos.Y - 20);

                        // Apply text scaling
                        ImGui.PushFont(ImGui.GetFont());
                        ImGui.SetWindowFontScale(Configuration.OverlayTextScale);
                        var killTextSize = ImGui.CalcTextSize(killText);
                        ImGui.PopFont();

                        // Adjust text position based on scaled size
                        textPos = new Vector2(playerScreenPos.X - killTextSize.X / 2, playerScreenPos.Y - 20);

                        // Draw text outline (black)
                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                            new Vector2(textPos.X - 1, textPos.Y), 0xFF000000, killText);
                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                            new Vector2(textPos.X + 1, textPos.Y), 0xFF000000, killText);
                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                            new Vector2(textPos.X, textPos.Y - 1), 0xFF000000, killText);
                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                            new Vector2(textPos.X, textPos.Y + 1), 0xFF000000, killText);

                        // Draw the main text
                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                            textPos, Configuration.KillTargetColor, killText);

                        // Add a small health percentage indicator
                        string healthText = $"{(int)(healthPercentage * 100)}%";
                        ImGui.PushFont(ImGui.GetFont());
                        ImGui.SetWindowFontScale(Configuration.OverlayTextScale);
                        var healthTextSize = ImGui.CalcTextSize(healthText);
                        ImGui.PopFont();

                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                            new Vector2(playerScreenPos.X - healthTextSize.X / 2, playerScreenPos.Y - 35),
                            Configuration.KillTargetColor,
                            healthText
                        );
                    }
                    // Apply additional visual indicators if needed
                    // Removed enemy targeting me icon code

                    // Add target indicator if applicable
                    if (isSharedTarget && Configuration.ShowShareCountText)
                    {
                        // Get the list of player names targeting this enemy
                        List<string> displayNames = [];
                        Dictionary<PlayerRole, List<string>> roleNames = new()
                        {
                            { PlayerRole.Tank, [] },
                            { PlayerRole.Healer, [] },
                            { PlayerRole.DPS, [] },
                            { PlayerRole.DoH, [] },
                            { PlayerRole.DoL, [] }
                        };

                        // Create a HashSet to track processed names and avoid duplicates
                        HashSet<string> processedNames = [];

                        // Sort players by role for better organization
                        foreach (string fullName in targetingPlayerNames)
                        {
                            // Skip if we've already processed this name
                            if (processedNames.Contains(fullName))
                                continue;

                            // Add to processed names to avoid duplicates
                            processedNames.Add(fullName);

                            // Get just the first name (before any spaces) to keep it short
                            string firstName = fullName.Contains(' ') ? fullName.Split(' ')[0] : fullName;

                            // Truncate long names
                            if (firstName.Length > 8)
                            {
                                firstName = $"{firstName[..6]}..";
                            }

                            // Try to find this player in the object table to determine their role and get job icon
                            PlayerRole playerRole = PlayerRole.DPS; // Default to DPS if we can't determine
                            string playerJobIcon = "❓"; // Default icon

                            foreach (var obj in ObjectTable)
                            {
                                if (obj is ICharacter character && character.Name.TextValue == fullName)
                                {
                                    playerRole = GetPlayerRole(character);
                                    playerJobIcon = GetPlayerJobIcon(character);
                                    break;
                                }
                            }

                            // Add to the appropriate role list with job icon
                            roleNames[playerRole].Add($"{playerJobIcon} {firstName}");
                        }

                        // Create role-specific formatted strings with role indicators
                        List<string> formattedRoleStrings = [];

                        // Add tanks with [T] prefix
                        if (roleNames[PlayerRole.Tank].Count > 0)
                        {
                            formattedRoleStrings.Add($"[T] {string.Join(", ", roleNames[PlayerRole.Tank])}");
                        }

                        // Add healers with [H] prefix
                        if (roleNames[PlayerRole.Healer].Count > 0)
                        {
                            formattedRoleStrings.Add($"[H] {string.Join(", ", roleNames[PlayerRole.Healer])}");
                        }

                        // Add DPS with [D] prefix
                        if (roleNames[PlayerRole.DPS].Count > 0)
                        {
                            // Limit DPS names if we already have tanks/healers to avoid too much text
                            int maxDps = Math.Max(1, 4 - roleNames[PlayerRole.Tank].Count - roleNames[PlayerRole.Healer].Count);
                            var dpsNames = roleNames[PlayerRole.DPS].Take(maxDps).ToList();

                            // If there are more DPS than we're showing, add a "+X" indicator
                            if (roleNames[PlayerRole.DPS].Count > maxDps)
                            {
                                dpsNames.Add($"+{roleNames[PlayerRole.DPS].Count - maxDps}");
                            }

                            formattedRoleStrings.Add($"[D] {string.Join(", ", dpsNames)}");
                        }

                        // Add DoH (crafters) with [C] prefix
                        if (roleNames[PlayerRole.DoH].Count > 0)
                        {
                            formattedRoleStrings.Add($"[C] {string.Join(", ", roleNames[PlayerRole.DoH])}");
                        }

                        // Add DoL (gatherers) with [G] prefix
                        if (roleNames[PlayerRole.DoL].Count > 0)
                        {
                            formattedRoleStrings.Add($"[G] {string.Join(", ", roleNames[PlayerRole.DoL])}");
                        }

                        // Join the role groups with line breaks
                        string targetText = string.Join("\n", formattedRoleStrings);

                        // If no names (shouldn't happen), show "TARGET"
                        if (string.IsNullOrEmpty(targetText))
                        {
                            targetText = "TARGET";
                        }

                        // Apply text scaling
                        ImGui.PushFont(ImGui.GetFont());
                        ImGui.SetWindowFontScale(Configuration.OverlayTextScale);

                        // Calculate text size for each line to get the widest one
                        float maxWidth = 0;
                        float totalHeight = 0;
                        foreach (var line in formattedRoleStrings)
                        {
                            var lineSize = ImGui.CalcTextSize(line);
                            maxWidth = Math.Max(maxWidth, lineSize.X);
                            totalHeight += lineSize.Y;
                        }

                        // Add some spacing between lines
                        totalHeight += (formattedRoleStrings.Count - 1) * 2;

                        ImGui.PopFont();

                        // Position the target text above the player
                        Vector2 targetTextPos = new(playerScreenPos.X - maxWidth / 2, playerScreenPos.Y - 50 - totalHeight);

                        // Choose base color based on whether it's a shared target
                        uint baseTextColor = targetingPlayerNames.Count > 1 ? 0xFFFFFF00 : Configuration.SharedTargetColor; // Yellow for shared targets

                        // Draw text background/outline for better visibility
                        // Use a semi-transparent black background with rounded corners
                        float bgPadding = 5.0f;
                        drawList.AddRectFilled(
                            new Vector2(targetTextPos.X - bgPadding, targetTextPos.Y - bgPadding),
                            new Vector2(targetTextPos.X + maxWidth + bgPadding, targetTextPos.Y + totalHeight + bgPadding),
                            0xC0000000, // More opaque black background
                            4.0f // Rounded corners
                        );

                        // Draw a colored border based on the number of players targeting
                        uint borderColor;
                        if (targetingPlayerNames.Count >= 4)
                            borderColor = 0xFFFF0000; // Red for 4+ players
                        else if (targetingPlayerNames.Count >= 3)
                            borderColor = 0xFFFFAA00; // Orange for 3 players
                        else if (targetingPlayerNames.Count >= 2)
                            borderColor = 0xFFFFFF00; // Yellow for 2 players
                        else
                            borderColor = Configuration.SharedTargetColor;

                        drawList.AddRect(
                            new Vector2(targetTextPos.X - bgPadding, targetTextPos.Y - bgPadding),
                            new Vector2(targetTextPos.X + maxWidth + bgPadding, targetTextPos.Y + totalHeight + bgPadding),
                            borderColor,
                            4.0f, // Rounded corners
                            ImDrawFlags.None,
                            1.5f // Border thickness
                        );

                        // Draw each line of text with appropriate role colors
                        float yOffset = 0;
                        foreach (var line in formattedRoleStrings)
                        {
                            uint textColor;
                            if (line.StartsWith("[T]"))
                                textColor = 0xFF4040FF; // Red for tanks (ABGR format)
                            else if (line.StartsWith("[H]"))
                                textColor = 0xFF40FF40; // Green for healers (ABGR format)
                            else if (line.StartsWith("[D]"))
                                textColor = 0xFFFF8040; // Blue for DPS (ABGR format)
                            else if (line.StartsWith("[C]"))
                                textColor = 0xFFFF00FF; // Purple for crafters (ABGR format)
                            else if (line.StartsWith("[G]"))
                                textColor = 0xFFFFFF00; // Yellow for gatherers (ABGR format)
                            else
                                textColor = baseTextColor;

                            // Draw the text with a slight black outline for better visibility
                            drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                                new Vector2(targetTextPos.X - 1, targetTextPos.Y + yOffset), 0xFF000000, line);
                            drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                                new Vector2(targetTextPos.X + 1, targetTextPos.Y + yOffset), 0xFF000000, line);
                            drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                                new Vector2(targetTextPos.X, targetTextPos.Y + yOffset - 1), 0xFF000000, line);
                            drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                                new Vector2(targetTextPos.X, targetTextPos.Y + yOffset + 1), 0xFF000000, line);

                            // Draw the main text
                            drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                                new Vector2(targetTextPos.X, targetTextPos.Y + yOffset), textColor, line);

                            // Move to the next line
                            yOffset += ImGui.CalcTextSize(line).Y + 2;
                        }

                        // Add a prominent indicator of how many players are targeting (for quick reference)
                        string countText = $"{targetingPlayerNames.Count}";
                        ImGui.PushFont(ImGui.GetFont());
                        ImGui.SetWindowFontScale(Configuration.OverlayTextScale * 1.2f); // Slightly larger
                        var countTextSize = ImGui.CalcTextSize(countText);
                        ImGui.PopFont();

                        // Position the count at the top right of the target box
                        Vector2 countPos = new(
                            targetTextPos.X + maxWidth + bgPadding - countTextSize.X - 2,
                            targetTextPos.Y - bgPadding - countTextSize.Y/2
                        );

                        // Draw count with a circle background
                        drawList.AddCircleFilled(
                            new Vector2(countPos.X + countTextSize.X/2, countPos.Y + countTextSize.Y/2),
                            countTextSize.X/2 + 4,
                            borderColor // Use the same color as the border
                        );

                        // Draw count text
                        drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale * 1.2f,
                            countPos, 0xFFFFFFFF, countText);
                    }

                    // Apply text scaling to player name/distance text
                    ImGui.PushFont(ImGui.GetFont());
                    ImGui.SetWindowFontScale(Configuration.OverlayTextScale);
                    var displayTextSize = ImGui.CalcTextSize(displayText);
                    ImGui.PopFont();

                    drawList.AddText(ImGui.GetFont(), ImGui.GetFont().FontSize * Configuration.OverlayTextScale,
                        new Vector2(playerScreenPos.X + 5, playerScreenPos.Y + 5),
                        Configuration.TextColor,
                        displayText
                    );



                    // Log for debugging
                    if (_playerCache.Count > 0 && _playerCache.IndexOf(player) == 0)
                    {
                        Log.Debug($"Drawing line to {player.Name.TextValue} at position {playerScreenPos.X:F1}, {playerScreenPos.Y:F1}");
                    }
                }
                catch (Exception ex)
                {
                    Log.Error($"Error drawing line to player: {ex.Message}");
                }
            }

            // Debug info
            if (_playerCache.Count == 0)
            {
                Log.Debug("No players found to draw lines to");
            }
            else
            {
                Log.Debug($"Found {_playerCache.Count} players to draw lines to");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Error in DrawLines: {ex.Message}");
        }
    }

    public void Dispose()
    {
        WindowSystem.RemoveAllWindows();

        ConfigWindow.Dispose();
        MainWindow.Dispose();

        CommandManager.RemoveHandler(CommandName);

        // Remove our event handlers
        PluginInterface.UiBuilder.Draw -= DrawUI;
        PluginInterface.UiBuilder.Draw -= DrawLines;
    }

    private void OnCommand(string command, string args)
    {
        // in response to the slash command, just toggle the display status of our main ui
        ToggleMainUI();
    }

    private void DrawUI()
    {
        // Draw the window system (main and config windows)
        WindowSystem.Draw();

        // Draw the debug window if enabled
        if (Configuration.ShowDebugWindow)
        {
            bool debugWindowVisible = true;
            ImGui.SetNextWindowSize(new Vector2(400, 500), ImGuiCond.FirstUseEver);
            if (ImGui.Begin("Debug", ref debugWindowVisible))
            {
                // Display shared target information
                ImGui.TextColored(new Vector4(1, 1, 0, 1), "Target Focus Detection Debug Info");
                ImGui.Separator();

                // Show current target
                if (TargetManager.Target is ICharacter currentTarget)
                {
                    ImGui.Text($"Current Target: {currentTarget.Name.TextValue}");
                    ImGui.Text($"HP: {currentTarget.CurrentHp}/{currentTarget.MaxHp} ({(float)currentTarget.CurrentHp / currentTarget.MaxHp * 100:F1}%)");
                }
                else
                {
                    ImGui.Text("No current target");
                }

                // Show focus target
                if (TargetManager.FocusTarget is ICharacter focusTarget)
                {
                    ImGui.Spacing();
                    ImGui.TextColored(new Vector4(1, 0.5f, 0, 1), $"Focus Target: {focusTarget.Name.TextValue}");
                    ImGui.Text($"HP: {focusTarget.CurrentHp}/{focusTarget.MaxHp} ({(float)focusTarget.CurrentHp / focusTarget.MaxHp * 100:F1}%)");
                    ImGui.Text($"Focus Target Tracking: {(Configuration.EnableFocusTargetTracking ? "Enabled" : "Disabled")}");
                }
                else
                {
                    ImGui.Spacing();
                    ImGui.Text("No focus target");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Text("Shared Targets:");

                // Display all shared targets being tracked
                if (_sharedTargets.Count > 0)
                {
                    foreach (var entry in _sharedTargets)
                    {
                        var targetInfo = entry.Value;
                        if (targetInfo.Target != null)
                        {
                            ImGui.TextColored(new Vector4(1, 0.8f, 0.2f, 1),
                                $"{targetInfo.Target.Name.TextValue} - {targetInfo.EstimatedAttackers} attackers");

                            // Show nearby players
                            ImGui.Indent(10);
                            ImGui.Text("Nearby players:");
                            foreach (var playerName in targetInfo.GetNearbyPlayerNames())
                            {
                                ImGui.Text($"- {playerName}");
                            }

                            // Show health tracking info
                            ImGui.Text($"HP: {targetInfo.Target.CurrentHp}/{targetInfo.Target.MaxHp}");
                            ImGui.Text($"Last HP change: {(DateTime.Now - targetInfo.LastHPChangeTime).TotalSeconds:F1}s ago");
                            ImGui.Text($"Is being attacked: {targetInfo.IsBeingAttacked}");
                            ImGui.Unindent(10);
                            ImGui.Spacing();
                        }
                    }
                }
                else
                {
                    ImGui.Text("No shared targets detected");
                }

                // Removed Invisible Ninjas debug section

                // Display additional debug information if needed

                // Removed Enemies Targeting You debug section

                ImGui.Separator();
                ImGui.Text("Party Members:");

                // Show party members
                if (ClientState.LocalPlayer != null)
                {
                    var partyMembers = ObjectTable
                        .Where(obj => obj.ObjectKind == ObjectKind.Player &&
                               obj is ICharacter character)
                        .OfType<ICharacter>()
                        .ToList();

                    var localPlayer = ClientState.LocalPlayer;
                    foreach (var member in partyMembers)
                    {
                        if (member != null && (member.StatusFlags.HasFlag(StatusFlags.PartyMember) || member.Address == localPlayer?.Address))
                        {
                            var role = GetPlayerRole(member);
                            Vector4 roleColor = role switch
                            {
                                PlayerRole.Tank => new(1.0f, 0.25f, 0.25f, 1.0f), // Red
                                PlayerRole.Healer => new(0.25f, 1.0f, 0.25f, 1.0f), // Green
                                PlayerRole.DPS => new(0.25f, 0.5f, 1.0f, 1.0f), // Blue
                                PlayerRole.DoH => new(1.0f, 0.0f, 1.0f, 1.0f), // Purple
                                PlayerRole.DoL => new(1.0f, 1.0f, 0.0f, 1.0f), // Yellow
                                _ => new(1.0f, 1.0f, 1.0f, 1.0f) // White (default)
                            };

                            // Get job icon
                            string jobIcon = GetPlayerJobIcon(member);

                            // Display player with job icon
                            ImGui.TextColored(roleColor, $"{jobIcon} {member.Name.TextValue} ({role})");

                            // Show their target if any
                            var memberTarget = GetPlayerTarget(member);
                            if (memberTarget != null)
                            {
                                ImGui.SameLine();
                                string targetJobIcon = GetPlayerJobIcon(memberTarget);
                                ImGui.Text($"→ {targetJobIcon} {memberTarget.Name.TextValue}");
                            }
                        }
                    }
                }
            }

            // Update the configuration if the window was closed
            if (!debugWindowVisible)
            {
                Configuration.ShowDebugWindow = false;
                Configuration.Save();
            }

            ImGui.End();
        }
    }

    public void ToggleConfigUI() => ConfigWindow.Toggle();
    public void ToggleMainUI() => MainWindow.Toggle();

    // UI toggle methods

    // Enum for player roles
    public enum PlayerRole
    {
        Tank,
        Healer,
        DPS,
        DoH,  // Disciples of the Hand (crafters)
        DoL   // Disciples of the Land (gatherers)
    }

    // Additional tracking dictionaries can be added here if needed



    // Get job icon character based on job ID
    private static string GetJobIcon(uint jobId)
    {
        // Map job IDs to simple text icons
        return jobId switch
        {
            // Tanks
            1 => "🛡️", // Gladiator
            3 => "🛡️", // Marauder
            19 => "🛡️", // Paladin
            21 => "🛡️", // Warrior
            32 => "🛡️", // Dark Knight
            37 => "🛡️", // Gunbreaker

            // Healers
            6 => "💚", // Conjurer
            24 => "💚", // White Mage
            28 => "💚", // Scholar
            33 => "💚", // Astrologian
            40 => "💚", // Sage

            // Melee DPS
            2 => "🗡️", // Pugilist
            4 => "🗡️", // Lancer
            20 => "🗡️", // Monk
            22 => "🗡️", // Dragoon
            29 => "🗡️", // Rogue
            30 => "🗡️", // Ninja
            34 => "🗡️", // Samurai
            39 => "🗡️", // Reaper

            // Ranged Physical DPS
            5 => "🏹", // Archer
            23 => "🏹", // Bard
            31 => "🏹", // Machinist
            38 => "🏹", // Dancer

            // Caster DPS
            7 => "🔮", // Thaumaturge
            25 => "🔮", // Black Mage
            26 => "🔮", // Arcanist
            27 => "🔮", // Summoner
            35 => "🔮", // Red Mage
            36 => "🔮", // Blue Mage

            // Disciples of the Hand (Crafters)
            8 => "🔨", // Carpenter
            9 => "⚒️", // Blacksmith
            10 => "🛠️", // Armorer
            11 => "💍", // Goldsmith
            12 => "🧵", // Leatherworker
            13 => "🧶", // Weaver
            14 => "⚗️", // Alchemist
            15 => "🍳", // Culinarian

            // Disciples of the Land (Gatherers)
            16 => "⛏️", // Miner
            17 => "🌿", // Botanist
            18 => "🎣", // Fisher

            // Default
            _ => "❓" // Unknown job
        };
    }

    // Dictionary to cache player job IDs
    private static readonly Dictionary<IntPtr, uint> _playerJobCache = [];

    // Determine the role of a player based on their job/class
    private static PlayerRole DeterminePlayerRole(ICharacter player)
    {
        if (player == null) return PlayerRole.DPS; // Default to DPS for null players

        try
        {
            // Try to access job ID using FFXIVClientStructs
            if (player is IPlayerCharacter playerCharacter)
            {
                try
                {
                    // Get the native pointer to the character
                    var address = playerCharacter.Address;
                    if (address != IntPtr.Zero)
                    {
                        // Try to get the character data from FFXIVClientStructs
                        unsafe
                        {
                            var character = (Character*)address;
                            if (character != null)
                            {
                                // Get the character's job ID
                                var jobId = (uint)character->CharacterData.ClassJob;

                                // Cache the job ID for this player
                                _playerJobCache[address] = jobId;

                                Log.Debug($"Found job ID {jobId} for player {player.Name.TextValue} using FFXIVClientStructs");

                                // TANKS - Red (ABGR: 0xFF0000FF)
                                // Tank jobs: Gladiator(1), Marauder(3), Paladin(19), Warrior(21), Dark Knight(32), Gunbreaker(37)
                                if (jobId == 1 || jobId == 3 || jobId == 19 || jobId == 21 || jobId == 32 || jobId == 37)
                                {
                                    Log.Debug($"Player {player.Name.TextValue} detected as TANK with job ID {jobId}");
                                    return PlayerRole.Tank;
                                }

                                // HEALERS - Green (ABGR: 0xFF00FF00)
                                // Healer jobs: Conjurer(6), White Mage(24), Scholar(28), Astrologian(33), Sage(40)
                                if (jobId == 6 || jobId == 24 || jobId == 28 || jobId == 33 || jobId == 40)
                                {
                                    Log.Debug($"Player {player.Name.TextValue} detected as HEALER with job ID {jobId}");
                                    return PlayerRole.Healer;
                                }

                                // DISCIPLES OF THE HAND (DoH) - Crafters
                                // Carpenter(8), Blacksmith(9), Armorer(10), Goldsmith(11), Leatherworker(12), Weaver(13), Alchemist(14), Culinarian(15)
                                if (jobId >= 8 && jobId <= 15)
                                {
                                    Log.Debug($"Player {player.Name.TextValue} detected as DoH (crafter) with job ID {jobId}");
                                    return PlayerRole.DoH;
                                }

                                // DISCIPLES OF THE LAND (DoL) - Gatherers
                                // Miner(16), Botanist(17), Fisher(18)
                                if (jobId >= 16 && jobId <= 18)
                                {
                                    Log.Debug($"Player {player.Name.TextValue} detected as DoL (gatherer) with job ID {jobId}");
                                    return PlayerRole.DoL;
                                }

                                // DPS - Blue (ABGR: 0xFFFF0000)
                                // All other combat jobs are DPS
                                Log.Debug($"Player {player.Name.TextValue} detected as DPS with job ID {jobId}");
                                return PlayerRole.DPS;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Debug($"Error accessing job data via FFXIVClientStructs: {ex.Message}. Falling back to name-based detection.");
                }
            }

            // Fallback to name-based detection if direct access fails
            var playerName = player.Name.TextValue.ToLower();

            // TANKS - Red (ABGR: 0xFF0000FF)
            if (playerName.Contains("tank") ||
                playerName.Contains("pld") || playerName.Contains("paladin") ||
                playerName.Contains("war") || playerName.Contains("warrior") ||
                playerName.Contains("drk") || playerName.Contains("dark knight") ||
                playerName.Contains("gnb") || playerName.Contains("gunbreaker") ||
                playerName.Contains("gladiator") || playerName.Contains("marauder"))
            {
                Log.Debug($"Player {player.Name.TextValue} detected as TANK based on name");
                return PlayerRole.Tank;
            }

            // HEALERS - Green (ABGR: 0xFF00FF00)
            if (playerName.Contains("heal") || playerName.Contains("healer") ||
                playerName.Contains("whm") || playerName.Contains("white mage") ||
                playerName.Contains("sch") || playerName.Contains("scholar") ||
                playerName.Contains("ast") || playerName.Contains("astrologian") ||
                playerName.Contains("sge") || playerName.Contains("sage") ||
                playerName.Contains("conjurer") ||
                playerName.Contains("medic") || playerName.Contains("doctor") ||
                playerName.Contains("cure") || playerName.Contains("regen") ||
                playerName.Contains("restore") || playerName.Contains("mend") ||
                playerName.Contains("tepes") || playerName.Contains("chiyoko"))
            {
                Log.Debug($"Player {player.Name.TextValue} detected as HEALER based on name");
                return PlayerRole.Healer;
            }

            // DISCIPLES OF THE HAND (DoH) - Crafters
            if (playerName.Contains("crafter") || playerName.Contains("craft") ||
                playerName.Contains("carpenter") || playerName.Contains("blacksmith") ||
                playerName.Contains("armorer") || playerName.Contains("goldsmith") ||
                playerName.Contains("leatherworker") || playerName.Contains("weaver") ||
                playerName.Contains("alchemist") || playerName.Contains("culinarian") ||
                playerName.Contains("artisan") || playerName.Contains("maker"))
            {
                Log.Debug($"Player {player.Name.TextValue} detected as DoH (crafter) based on name");
                return PlayerRole.DoH;
            }

            // DISCIPLES OF THE LAND (DoL) - Gatherers
            if (playerName.Contains("gatherer") || playerName.Contains("gather") ||
                playerName.Contains("miner") || playerName.Contains("botanist") ||
                playerName.Contains("fisher") || playerName.Contains("harvester") ||
                playerName.Contains("collector") || playerName.Contains("forager"))
            {
                Log.Debug($"Player {player.Name.TextValue} detected as DoL (gatherer) based on name");
                return PlayerRole.DoL;
            }

            // DPS - Blue (ABGR: 0xFFFF0000) (default)
            Log.Debug($"Player {player.Name.TextValue} detected as DPS (default)");
            return PlayerRole.DPS;
        }
        catch (Exception ex)
        {
            Log.Debug($"Error in role detection: {ex.Message}");
            return PlayerRole.DPS; // Default to DPS if there's an error
        }
    }



    // Cache for player health percentages
    private readonly Dictionary<IntPtr, (float Percentage, DateTime LastUpdate)> _healthCache = [];
    private DateTime _lastHealthCacheCleanup = DateTime.MinValue;
    private const int HEALTH_CACHE_UPDATE_INTERVAL_MS = 100; // Update health cache every 100ms
    private const int HEALTH_CACHE_CLEANUP_INTERVAL_MS = 5000; // Clean up health cache every 5 seconds

    // Get the health percentage of a player
    private float GetPlayerHealthPercentage(ICharacter player)
    {
        try
        {
            // Clean up health cache periodically
            if ((DateTime.Now - _lastHealthCacheCleanup).TotalMilliseconds >= HEALTH_CACHE_CLEANUP_INTERVAL_MS)
            {
                _healthCache.Clear();
                _lastHealthCacheCleanup = DateTime.Now;
            }

            // Check if we have a recent cached value
            if (_healthCache.TryGetValue(player.Address, out var cachedHealth) &&
                (DateTime.Now - cachedHealth.LastUpdate).TotalMilliseconds < HEALTH_CACHE_UPDATE_INTERVAL_MS)
            {
                return cachedHealth.Percentage;
            }

            // Calculate health percentage
            float healthPercentage = player.MaxHp > 0 ? (float)player.CurrentHp / player.MaxHp : 1.0f;

            // Cache the result
            _healthCache[player.Address] = (healthPercentage, DateTime.Now);

            return healthPercentage;
        }
        catch (Exception ex)
        {
            Log.Error($"Error getting player health: {ex.Message}");
            return 1.0f; // Default to full health if there's an error
        }
    }


    // Stub for IsTargetingLocalPlayer method (kept for compatibility)
    public static bool IsTargetingLocalPlayer()
    {
        return false;
    }
}