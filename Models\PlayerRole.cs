using System;
using System.Collections.Generic;
using Dalamud.Game.ClientState.Objects.Types;
using Dalamud.Plugin.Services;
using FFXIVClientStructs.FFXIV.Client.Game.Character;

namespace CCLineDrawer.Models
{
    /// <summary>
    /// Represents different player roles in FFXIV
    /// </summary>
    public enum PlayerRole
    {
        Tank,
        Healer,
        DPS,
        DoH,  // Disciples of the Hand (crafters)
        DoL   // Disciples of the Land (gatherers)
    }

    /// <summary>
    /// Service for determining player roles and job information
    /// </summary>
    public static class PlayerRoleService
    {
        // Dictionary to cache player job IDs for performance
        private static readonly Dictionary<IntPtr, uint> _playerJobCache = new();

        /// <summary>
        /// Determines the role of a player based on their job/class
        /// </summary>
        public static PlayerRole DeterminePlayerRole(ICharacter player, IPluginLog log)
        {
            if (player == null) return PlayerRole.DPS;

            try
            {
                if (player is IPlayerCharacter player<PERSON>haracter)
                {
                    try
                    {
                        var address = playerCharacter.Address;
                        if (address != IntPtr.Zero)
                        {
                            unsafe
                            {
                                var character = (Character*)address;
                                if (character != null)
                                {
                                    var jobId = (uint)character->CharacterData.ClassJob;
                                    _playerJobCache[address] = jobId;

                                    log.Debug($"Found job ID {jobId} for player {player.Name.TextValue}");

                                    return GetRoleFromJobId(jobId);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        log.Debug($"Error accessing job data: {ex.Message}. Falling back to name-based detection.");
                    }
                }

                // Fallback to name-based detection
                return GetRoleFromPlayerName(player.Name.TextValue, log);
            }
            catch
            {
                return PlayerRole.DPS;
            }
        }

        private static PlayerRole GetRoleFromJobId(uint jobId)
        {
            // Tanks
            if (jobId == 1 || jobId == 3 || jobId == 19 || jobId == 21 || jobId == 32 || jobId == 37)
                return PlayerRole.Tank;

            // Healers
            if (jobId == 6 || jobId == 24 || jobId == 28 || jobId == 33 || jobId == 40)
                return PlayerRole.Healer;

            // Disciples of the Hand (Crafters)
            if (jobId >= 8 && jobId <= 15)
                return PlayerRole.DoH;

            // Disciples of the Land (Gatherers)
            if (jobId >= 16 && jobId <= 18)
                return PlayerRole.DoL;

            // All other combat jobs are DPS
            return PlayerRole.DPS;
        }

        private static PlayerRole GetRoleFromPlayerName(string playerName, IPluginLog log)
        {
            var name = playerName.ToLower();

            // Tank detection
            if (name.Contains("tank") || name.Contains("pld") || name.Contains("paladin") ||
                name.Contains("war") || name.Contains("warrior") || name.Contains("drk") ||
                name.Contains("dark knight") || name.Contains("gnb") || name.Contains("gunbreaker") ||
                name.Contains("gladiator") || name.Contains("marauder"))
            {
                log.Debug($"Player {playerName} detected as TANK based on name");
                return PlayerRole.Tank;
            }

            // Healer detection
            if (name.Contains("heal") || name.Contains("whm") || name.Contains("white mage") ||
                name.Contains("sch") || name.Contains("scholar") || name.Contains("ast") ||
                name.Contains("astrologian") || name.Contains("sage") || name.Contains("conjurer"))
            {
                log.Debug($"Player {playerName} detected as HEALER based on name");
                return PlayerRole.Healer;
            }

            // Crafter detection
            if (name.Contains("craft") || name.Contains("carpenter") || name.Contains("blacksmith") ||
                name.Contains("armorer") || name.Contains("goldsmith") || name.Contains("leatherworker") ||
                name.Contains("weaver") || name.Contains("alchemist") || name.Contains("culinarian"))
            {
                log.Debug($"Player {playerName} detected as DoH based on name");
                return PlayerRole.DoH;
            }

            // Gatherer detection
            if (name.Contains("gather") || name.Contains("miner") || name.Contains("botanist") ||
                name.Contains("fisher") || name.Contains("collector") || name.Contains("forager"))
            {
                log.Debug($"Player {playerName} detected as DoL based on name");
                return PlayerRole.DoL;
            }

            log.Debug($"Player {playerName} detected as DPS (default)");
            return PlayerRole.DPS;
        }

        /// <summary>
        /// Gets a job icon for the player based on their cached job ID or role
        /// </summary>
        public static string GetPlayerJobIcon(ICharacter player)
        {
            try
            {
                if (player == null) return "❓";

                if (_playerJobCache.TryGetValue(player.Address, out uint jobId))
                {
                    return GetJobIcon(jobId);
                }

                // Fallback to role-based icon
                var role = DeterminePlayerRole(player, null);
                return role switch
                {
                    PlayerRole.Tank => "🛡️",
                    PlayerRole.Healer => "💚",
                    PlayerRole.DPS => "🗡️",
                    PlayerRole.DoH => "🔨",
                    PlayerRole.DoL => "🌿",
                    _ => "❓"
                };
            }
            catch
            {
                return "❓";
            }
        }

        private static string GetJobIcon(uint jobId)
        {
            return jobId switch
            {
                // Tanks
                1 or 3 or 19 or 21 or 32 or 37 => "🛡️",
                // Healers
                6 or 24 or 28 or 33 or 40 => "💚",
                // Melee DPS
                2 or 4 or 20 or 22 or 29 or 30 or 34 or 39 => "🗡️",
                // Ranged Physical DPS
                5 or 23 or 31 or 38 => "🏹",
                // Caster DPS
                7 or 25 or 26 or 27 or 35 or 36 => "🔮",
                // Crafters
                >= 8 and <= 15 => "🔨",
                // Gatherers
                >= 16 and <= 18 => "🌿",
                _ => "❓"
            };
        }
    }
}
