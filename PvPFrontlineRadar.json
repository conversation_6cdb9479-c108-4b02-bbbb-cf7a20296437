{"Author": "lucy2000", "Name": "PvP Frontline Radar", "Punchline": "Enhanced radar for Frontline PvP with player tracking features.", "Description": "A plugin that provides enhanced radar functionality for Frontline PvP, including player tracking, focus target tracking, and visual indicators to help with positioning and targeting. Use /pvpradar to open the configuration window.", "RepoUrl": "https://gitlab.com/lucy2000/PvPFrontlineRadar", "ApplicableVersion": "any", "Tags": ["pvp", "frontline", "radar", "tracking"], "CategoryTags": ["UI", "PvP"], "DalamudApiLevel": 12, "LoadRequiredState": 0, "LoadSync": false, "LoadPriority": 0, "IconUrl": "", "Changelog": "Initial release - Frontline PvP radar with player tracking features"}