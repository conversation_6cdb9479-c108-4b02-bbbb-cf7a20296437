﻿using System;
using System.Numerics;
using System.Linq;
using System.Collections.Generic;
using Dalamud.Interface.Utility;
using Dalamud.Interface.Utility.Raii;
using Dalamud.Interface.Windowing;
using Dalamud.Game.ClientState.Objects.Types;
using ImGuiNET;
using Lumina.Excel.Sheets;
using Newtonsoft.Json;

namespace CCLineDrawer.Windows;

public class MainWindow : Window, IDisposable
{

    private Plugin Plugin;

    // We give this window a hidden ID using ##
    // So that the user will see "My Amazing Window" as window title,
    // but for ImGui the ID is "My Amazing Window##With a hidden ID"
    public MainWindow(Plugin plugin)
        : base("Crystalline Conflict Line Drawer##CCLineDrawerMain", ImGuiWindowFlags.NoScrollbar | ImGuiWindowFlags.NoScrollWithMouse)
    {
        SizeConstraints = new WindowSizeConstraints
        {
            MinimumSize = new(375, 330),
            MaximumSize = new(float.MaxValue, float.MaxValue)
        };

        Plugin = plugin;
    }

    public override void PreDraw()
    {
        // Apply window position if locked
        if (Plugin.Configuration.LockMainWindowPosition)
        {
            Position = Plugin.Configuration.MainWindowPosition;
            PositionCondition = ImGuiCond.Always;
        }
    }

    public void Dispose() { }

    public override void Draw()
    {
        // Apply theme colors
        // Convert uint colors to Vector4 for ImGui
        Vector4 bgColor = ImGui.ColorConvertU32ToFloat4(Plugin.Configuration.UiBackgroundColor);
        Vector4 textColor = ImGui.ColorConvertU32ToFloat4(Plugin.Configuration.UiTextColor);

        // Log the colors being applied
        Plugin.Log.Debug($"Applying colors - BG: {Plugin.Configuration.UiBackgroundColor:X8}, Text: {Plugin.Configuration.UiTextColor:X8}");
        Plugin.Log.Debug($"BG Vector4: {bgColor.X}, {bgColor.Y}, {bgColor.Z}, {bgColor.W}");

        ImGui.PushStyleColor(ImGuiCol.WindowBg, bgColor);
        ImGui.PushStyleColor(ImGuiCol.Text, textColor);

        // Apply UI scaling
        ImGui.PushFont(ImGui.GetFont());
        ImGui.SetWindowFontScale(Plugin.Configuration.UiScale);

        // Header with theme color
        Vector4 headerColor = ImGui.ColorConvertU32ToFloat4(Plugin.Configuration.UiHeaderColor);
        Plugin.Log.Debug($"Header color: {Plugin.Configuration.UiHeaderColor:X8} -> Vector4: {headerColor.X}, {headerColor.Y}, {headerColor.Z}, {headerColor.W}");

        ImGui.PushStyleColor(ImGuiCol.Text, headerColor);
        ImGui.TextUnformatted("Crystalline Conflict Line Drawer");
        ImGui.PopStyleColor();
        ImGui.Separator();

        // Status information
        var isLineEnabled = Plugin.Configuration.EnableLineDrawing;
        ImGui.TextUnformatted($"Line Drawing: {(isLineEnabled ? "Enabled" : "Disabled")}");

        // Check if we're in Crystalline Conflict
        var territoryId = Plugin.ClientState.TerritoryType;
        bool isInCC = territoryId is 1122 or 1123 or 1124 or 1125 or 1126;
        bool isPvPZone = Plugin.IsInPvPZonePublic();
        bool hasEnemyPlayers = Plugin.HasEnemyPlayersNearbyPublic();

        if (isInCC)
        {
            ImGui.TextColored(new(0, 1, 0, 1), "You are currently in Crystalline Conflict!");

            // Get the map name
            if (Plugin.DataManager.GetExcelSheet<TerritoryType>().TryGetRow(territoryId, out var territoryRow))
            {
                ImGui.TextUnformatted($"Current Map: {territoryRow.PlaceName.Value.Name.ExtractText()}");
            }
        }
        else if (isPvPZone)
        {
            ImGui.TextColored(new(0, 0.8f, 1, 1), "You are in a PvP area!");

            // Get the map name if available
            if (Plugin.DataManager.GetExcelSheet<TerritoryType>().TryGetRow(territoryId, out var territoryRow))
            {
                ImGui.TextUnformatted($"Current Area: {territoryRow.PlaceName.Value.Name.ExtractText()}");
            }

            ImGui.TextUnformatted("PvP features are active in this area.");
        }
        else if (hasEnemyPlayers)
        {
            ImGui.TextColored(new(1, 0.8f, 0, 1), "Enemy players detected nearby!");
            ImGui.TextColored(new(1, 0.5f, 0, 1), "Note: Enemy targeting indicators only work in PvP instances.");
        }
        else
        {
            ImGui.TextColored(new(1, 0.5f, 0, 1), "You are not in a PvP area.");
            ImGui.TextUnformatted("PvP features will activate in PvP instances only.");

            // Removed Enemy Targeting Me feature
        }

        ImGui.Separator();

        // Settings button
        if (ImGui.Button("Open Settings"))
        {
            Plugin.ToggleConfigUI();
        }

        ImGui.SameLine();

        // Toggle buttons
        if (ImGui.Button(isLineEnabled ? "Disable Line Drawing" : "Enable Line Drawing"))
        {
            Plugin.Configuration.EnableLineDrawing = !isLineEnabled;
            Plugin.Configuration.Save();
        }



        ImGui.Separator();

        // Removed Enemies Targeting You section

        // Debug window toggle
        var showDebugWindow = Plugin.Configuration.ShowDebugWindow;
        if (ImGui.Button(showDebugWindow ? "Hide Debug Window" : "Show Debug Window"))
        {
            Plugin.Configuration.ShowDebugWindow = !showDebugWindow;
            Plugin.Configuration.Save();
        }

        ImGui.Separator();

        // Instructions
        ImGui.TextUnformatted("Instructions:");
        ImGui.BulletText("PvP features work in Crystalline Conflict and other PvP areas");
        ImGui.BulletText("Enemy targeting detection works whenever enemy players are nearby");
        ImGui.BulletText("Lines will be drawn to other players based on your settings");
        ImGui.BulletText("Use the settings window to customize appearance and options");
        ImGui.BulletText("You can toggle features on/off with the buttons above");

        ImGui.Spacing();
        ImGui.TextColored(new(1, 0.8f, 0.2f, 1), "PvP Combat Tips:");
        ImGui.BulletText("Practice positioning with different job abilities before matches");
        ImGui.BulletText("Learn the range of enemy gap closers to avoid being caught");
        ImGui.BulletText("Understand your healing ranges to support allies effectively");
        ImGui.BulletText("Visualize AoE ranges to maximize damage on grouped enemies");

        ImGui.Separator();

        // Credits
        ImGui.TextUnformatted("Created using Dalamud Plugin Framework");
        ImGui.TextUnformatted("Based on SamplePlugin template");

        // Pop style colors and font scaling
        ImGui.PopFont();
        ImGui.PopStyleColor(2); // Pop the two style colors we pushed at the beginning
    }
}
