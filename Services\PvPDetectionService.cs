using System;
using System.Collections.Generic;
using System.Linq;
using Dalamud.Game.ClientState.Objects.Types;
using Dalamud.Game.ClientState.Objects.Enums;
using Dalamud.Plugin.Services;

namespace CCLineDrawer.Services
{
    /// <summary>
    /// Service responsible for detecting PvP zones and enemy players
    /// </summary>
    public class PvPDetectionService
    {
        private readonly IClientState _clientState;
        private readonly IObjectTable _objectTable;
        private readonly IPluginLog _log;

        // Known PvP territory IDs
        private static readonly HashSet<ushort> PvPTerritoryIds = new()
        {
            // Crystalline Conflict arenas
            1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131,
            // Frontline maps
            376, 431, 554, 888,
            353, 354, 355, 356, 357, 358, 378, 379, 422, 423, 424, 425, 426, 427, 428, 429, 430,
            // The Feast / Rival Wings maps
            791, 1065,
            689, 690, 691, 692, 693, 794, 795, 796, 797, 798,
            // PvP preparation areas
            250, 1031, 1032, 1033,
            // PvP training areas
            1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020,
            // Other PvP-related zones
            1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050
        };

        public PvPDetectionService(IClientState clientState, IObjectTable objectTable, IPluginLog log)
        {
            _clientState = clientState;
            _objectTable = objectTable;
            _log = log;
        }

        public bool IsInPvPZone()
        {
            try
            {
                var territoryType = _clientState.TerritoryType;
                var isInPvPZone = PvPTerritoryIds.Contains(territoryType);

                // If not in a recognized PvP zone, check for enemy players
                if (!isInPvPZone && _clientState.LocalPlayer != null && HasEnemyPlayersNearby())
                {
                    _log.Debug("PvP detected based on enemy players nearby");
                    return true;
                }

                return isInPvPZone;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error checking if in PvP zone");
                return false;
            }
        }

        public bool IsInCrystallineConflict()
        {
            var territoryId = _clientState.TerritoryType;
            return territoryId is >= 1122 and <= 1131;
        }

        public bool HasEnemyPlayersNearby()
        {
            try
            {
                var localPlayer = _clientState.LocalPlayer;
                if (localPlayer == null) return false;

                foreach (var obj in _objectTable)
                {
                    if (obj.ObjectKind == ObjectKind.Player &&
                        obj.Address != localPlayer.Address &&
                        obj is ICharacter character &&
                        !character.StatusFlags.HasFlag(StatusFlags.PartyMember) &&
                        character.CurrentHp > 0)
                    {
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error checking for enemy players nearby");
                return false;
            }
        }

        public List<ICharacter> GetEnemyPlayers()
        {
            var enemies = new List<ICharacter>();
            var localPlayer = _clientState.LocalPlayer;
            
            if (localPlayer == null) return enemies;

            foreach (var obj in _objectTable)
            {
                if (obj.ObjectKind == ObjectKind.Player &&
                    obj.Address != localPlayer.Address &&
                    obj is ICharacter character &&
                    !character.StatusFlags.HasFlag(StatusFlags.PartyMember) &&
                    character.CurrentHp > 0)
                {
                    enemies.Add(character);
                }
            }

            return enemies;
        }

        public List<ICharacter> GetAllyPlayers()
        {
            var allies = new List<ICharacter>();
            var localPlayer = _clientState.LocalPlayer;
            
            if (localPlayer == null) return allies;

            foreach (var obj in _objectTable)
            {
                if (obj.ObjectKind == ObjectKind.Player &&
                    obj.Address != localPlayer.Address &&
                    obj is ICharacter character &&
                    character.StatusFlags.HasFlag(StatusFlags.PartyMember) &&
                    character.CurrentHp > 0)
                {
                    allies.Add(character);
                }
            }

            return allies;
        }
    }
}
