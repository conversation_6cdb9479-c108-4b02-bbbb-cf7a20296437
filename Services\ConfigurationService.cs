using System;
using System.ComponentModel.DataAnnotations;
using System.Numerics;
using CCLineDrawer.Services;

namespace CCLineDrawer.Services
{
    /// <summary>
    /// Service for managing and validating configuration
    /// </summary>
    public class ConfigurationService
    {
        private readonly LoggingService _logger;
        private Configuration _configuration;

        public ConfigurationService(Configuration configuration, LoggingService logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public bool ValidateConfiguration()
        {
            try
            {
                ValidateLineSettings();
                ValidateColorSettings();
                ValidatePerformanceSettings();
                ValidateUISettings();

                _logger.LogInfo("Configuration validation passed");
                return true;
            }
            catch (ValidationException ex)
            {
                _logger.LogError(ex, "Configuration validation failed");
                return false;
            }
        }

        private void ValidateLineSettings()
        {
            if (_configuration.LineThickness < 0.1f || _configuration.LineThickness > 20.0f)
            {
                _configuration.LineThickness = Math.Clamp(_configuration.LineThickness, 0.1f, 20.0f);
                _logger.LogWarning($"Line thickness clamped to {_configuration.LineThickness}");
            }

            if (_configuration.MaxDrawDistance < 5.0f || _configuration.MaxDrawDistance > 200.0f)
            {
                _configuration.MaxDrawDistance = Math.Clamp(_configuration.MaxDrawDistance, 5.0f, 200.0f);
                _logger.LogWarning($"Max draw distance clamped to {_configuration.MaxDrawDistance}");
            }
        }

        private void ValidateColorSettings()
        {
            // Validate that colors have proper alpha values
            _configuration.LineColor = ValidateColor(_configuration.LineColor, "LineColor");
            _configuration.TextColor = ValidateColor(_configuration.TextColor, "TextColor");
            _configuration.TankColor = ValidateColor(_configuration.TankColor, "TankColor");
            _configuration.HealerColor = ValidateColor(_configuration.HealerColor, "HealerColor");
            _configuration.DPSColor = ValidateColor(_configuration.DPSColor, "DPSColor");
        }

        private uint ValidateColor(uint color, string colorName)
        {
            // Ensure alpha channel is not zero (invisible)
            uint alpha = (color >> 24) & 0xFF;
            if (alpha < 0x10) // Less than ~6% opacity
            {
                color = (color & 0x00FFFFFF) | 0xFF000000; // Set to full opacity
                _logger.LogWarning($"{colorName} had very low alpha, set to full opacity");
            }
            return color;
        }

        private void ValidatePerformanceSettings()
        {
            if (_configuration.OverlayTextScale < 0.1f || _configuration.OverlayTextScale > 5.0f)
            {
                _configuration.OverlayTextScale = Math.Clamp(_configuration.OverlayTextScale, 0.1f, 5.0f);
                _logger.LogWarning($"Overlay text scale clamped to {_configuration.OverlayTextScale}");
            }
        }

        private void ValidateUISettings()
        {
            if (_configuration.UiScale < 0.1f || _configuration.UiScale > 5.0f)
            {
                _configuration.UiScale = Math.Clamp(_configuration.UiScale, 0.1f, 5.0f);
                _logger.LogWarning($"UI scale clamped to {_configuration.UiScale}");
            }

            // Validate window position is on screen
            ValidateWindowPosition();
        }

        private void ValidateWindowPosition()
        {
            var pos = _configuration.MainWindowPosition;

            // Basic bounds checking (assuming minimum 800x600 screen)
            if (pos.X < -100 || pos.X > 1920 || pos.Y < -100 || pos.Y > 1080)
            {
                _configuration.MainWindowPosition = new Vector2(100, 100);
                _logger.LogWarning("Window position was off-screen, reset to default");
            }
        }

        public void ResetToDefaults()
        {
            _logger.LogInfo("Resetting configuration to defaults");

            _configuration.LineThickness = 2.0f;
            _configuration.LineColor = 0xFF00FFFF;
            _configuration.TextColor = 0xFFFFFFFF;
            _configuration.MaxDrawDistance = 50.0f;
            _configuration.OverlayTextScale = 0.8f;
            _configuration.UiScale = 1.0f;
            _configuration.MainWindowPosition = new Vector2(100, 100);

            // Reset theme to default
            _configuration.SelectedTheme = UiTheme.Default;
            _configuration.ApplyThemeColors();

            _configuration.Save();
        }

        public void MigrateConfiguration(int fromVersion, int toVersion)
        {
            _logger.LogInfo($"Migrating configuration from version {fromVersion} to {toVersion}");

            // Add migration logic here as the plugin evolves
            switch (fromVersion)
            {
                case 0:
                    // Migration from version 0 to 1
                    if (_configuration.LineThickness == 0)
                        _configuration.LineThickness = 2.0f;
                    break;

                // Add more migration cases as needed
            }

            _configuration.Version = toVersion;
            _configuration.Save();
        }
    }
}
