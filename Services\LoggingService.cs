using System;
using System.Runtime.CompilerServices;
using Dalamud.Plugin.Services;

namespace CCLineDrawer.Services
{
    /// <summary>
    /// Centralized logging service with performance considerations
    /// </summary>
    public class LoggingService
    {
        private readonly IPluginLog _log;
        private readonly bool _isDebugMode;

        public LoggingService(IPluginLog log, bool isDebugMode = false)
        {
            _log = log;
            _isDebugMode = isDebugMode;
        }

        public void LogInfo(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "")
        {
            _log.Information($"[{GetClassName(filePath)}.{memberName}] {message}");
        }

        public void LogWarning(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "")
        {
            _log.Warning($"[{GetClassName(filePath)}.{memberName}] {message}");
        }

        public void LogError(Exception ex, string message = "", [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "")
        {
            var fullMessage = string.IsNullOrEmpty(message) ? ex.Message : $"{message}: {ex.Message}";
            _log.Error(ex, $"[{GetClassName(filePath)}.{memberName}] {fullMessage}");
        }

        public void LogDebug(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "")
        {
            if (_isDebugMode)
            {
                _log.Debug($"[{GetClassName(filePath)}.{memberName}] {message}");
            }
        }

        public void LogPerformance(string operation, long elapsedMs, [CallerMemberName] string memberName = "")
        {
            if (elapsedMs > 16) // More than one frame at 60fps
            {
                LogWarning($"Performance: {operation} took {elapsedMs}ms", memberName);
            }
            else if (_isDebugMode)
            {
                LogDebug($"Performance: {operation} took {elapsedMs}ms", memberName);
            }
        }

        private static string GetClassName(string filePath)
        {
            var fileName = System.IO.Path.GetFileNameWithoutExtension(filePath);
            return fileName;
        }
    }

    /// <summary>
    /// Exception handling utilities
    /// </summary>
    public static class ExceptionHandler
    {
        public static T SafeExecute<T>(Func<T> operation, T defaultValue, LoggingService logger, string operationName = "")
        {
            try
            {
                return operation();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error in {operationName}");
                return defaultValue;
            }
        }

        public static void SafeExecute(Action operation, LoggingService logger, string operationName = "")
        {
            try
            {
                operation();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error in {operationName}");
            }
        }

        public static bool TryExecute(Action operation, LoggingService logger, string operationName = "")
        {
            try
            {
                operation();
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error in {operationName}");
                return false;
            }
        }
    }
}
