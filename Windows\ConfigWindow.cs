﻿using System;
using System.Numerics;
using System.Linq;
using System.Collections.Generic;
using Dalamud.Interface.Windowing;
using ImGuiNET;
using static ImGuiNET.ImGuiColorEditFlags;
using Newtonsoft.Json;

namespace CCLineDrawer.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;
    private Plugin Plugin;

    // We give this window a constant ID using ###
    // This allows for labels being dynamic, like "{FPS Counter}fps###XYZ counter window",
    // and the window ID will always be "###XYZ counter window" for ImGui
    public ConfigWindow(Plugin plugin) : base("Crystalline Conflict Line Drawer Configuration###CCLineDrawerConfig")
    {
        Flags = ImGuiWindowFlags.NoCollapse;

        Size = new(500, 500);
        SizeCondition = ImGuiCond.FirstUseEver;

        Plugin = plugin;
        Configuration = plugin.Configuration;
    }

    public void Dispose() { }

    public override void PreDraw()
    {
        // Flags must be added or removed before Draw() is being called, or they won't apply
        if (Configuration.IsConfigWindowMovable)
        {
            Flags &= ~ImGuiWindowFlags.NoMove;
        }
        else
        {
            Flags |= ImGuiWindowFlags.NoMove;
        }
    }

    public override void Draw()
    {
        // Apply theme colors
        // Convert uint colors to Vector4 for ImGui
        Vector4 bgColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiBackgroundColor);
        Vector4 textColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiTextColor);

        // Log the colors being applied
        Plugin.Log.Debug($"ConfigWindow - Applying colors - BG: {Configuration.UiBackgroundColor:X8}, Text: {Configuration.UiTextColor:X8}");
        Plugin.Log.Debug($"ConfigWindow - BG Vector4: {bgColor.X}, {bgColor.Y}, {bgColor.Z}, {bgColor.W}");

        ImGui.PushStyleColor(ImGuiCol.WindowBg, bgColor);
        ImGui.PushStyleColor(ImGuiCol.Text, textColor);

        // Apply UI scaling
        ImGui.PushFont(ImGui.GetFont());
        ImGui.SetWindowFontScale(Configuration.UiScale);

        ImGui.PushStyleVar(ImGuiStyleVar.ItemSpacing, new Vector2(8, 10)); // Increase spacing between items

        // Header with theme color
        Vector4 headerColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiHeaderColor);
        Plugin.Log.Debug($"ConfigWindow - Header color: {Configuration.UiHeaderColor:X8} -> Vector4: {headerColor.X}, {headerColor.Y}, {headerColor.Z}, {headerColor.W}");

        ImGui.PushStyleColor(ImGuiCol.Text, headerColor);
        ImGui.TextUnformatted("Crystalline Conflict Line Drawer Settings");
        ImGui.PopStyleColor();
        ImGui.Separator();
        ImGui.Spacing();

        // Main toggle with larger font
        ImGui.PushFont(ImGui.GetFont());
        ImGui.PushStyleColor(ImGuiCol.Text, 0xFF80FFFF);
        var enableLineDrawing = Configuration.EnableLineDrawing;
        if (ImGui.Checkbox("Enable Line Drawing", ref enableLineDrawing))
        {
            Configuration.EnableLineDrawing = enableLineDrawing;
            Configuration.Save();
        }
        ImGui.PopStyleColor();
        ImGui.PopFont();

        ImGui.Spacing();

        if (Configuration.EnableLineDrawing)
        {
            // Create a tabbed interface for better organization
            if (ImGui.BeginTabBar("SettingsTabs"))
            {
                // Appearance Tab
                if (ImGui.BeginTabItem("Appearance"))
                {
                    ImGui.Spacing();
                    ImGui.TextColored(new(0.7f, 1, 0.7f, 1), "Line Appearance");
                    ImGui.Separator();
                    ImGui.Spacing();

                    ImGui.TextWrapped("This tab controls the visual appearance of the lines drawn between you and other players. Customize the thickness and colors to make the lines more visible or match your UI theme.");
                    ImGui.TextWrapped("The appearance settings affect all lines unless overridden by role-based coloring or target highlighting features.");
                    ImGui.Spacing();

                    // Line thickness
                    var lineThickness = Configuration.LineThickness;
                    if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 0.5f, 10.0f, "%.1f"))
                    {
                        Configuration.LineThickness = lineThickness;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Controls how thick the lines appear. Thicker lines are more visible but may be distracting. Recommended values:");
                    ImGui.BulletText("1.0-2.0: Subtle lines that don't obstruct view");
                    ImGui.BulletText("3.0-5.0: Medium thickness for better visibility");
                    ImGui.BulletText("6.0-10.0: Very thick lines for maximum visibility");
                    ImGui.Spacing();

                    // Line color
                    var lineColor = ImGui.ColorConvertU32ToFloat4(Configuration.LineColor);
                    if (ImGui.ColorEdit4("Line Color", ref lineColor, AlphaPreview | AlphaBar))
                    {
                        byte r = (byte)(lineColor.X * 255.0f);
                        byte g = (byte)(lineColor.Y * 255.0f);
                        byte b = (byte)(lineColor.Z * 255.0f);
                        byte a = (byte)(lineColor.W * 255.0f);
                        Configuration.LineColor = Configuration.ImGuiColorToUint(r, g, b, a);
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Sets the color of all lines. Choose a color that stands out against the game environment but isn't too distracting. The alpha channel controls transparency.");
                    ImGui.BulletText("Bright colors like cyan, yellow, or magenta are highly visible");
                    ImGui.BulletText("Reduce alpha (transparency) to make lines less intrusive");
                    ImGui.BulletText("Consider using colors that contrast with the game environment");
                    ImGui.Spacing();

                    // Text color
                    var lineTextColor = ImGui.ColorConvertU32ToFloat4(Configuration.TextColor);
                    if (ImGui.ColorEdit4("Text Color", ref lineTextColor, AlphaPreview | AlphaBar))
                    {
                        byte r = (byte)(lineTextColor.X * 255.0f);
                        byte g = (byte)(lineTextColor.Y * 255.0f);
                        byte b = (byte)(lineTextColor.Z * 255.0f);
                        byte a = (byte)(lineTextColor.W * 255.0f);
                        Configuration.TextColor = Configuration.ImGuiColorToUint(r, g, b, a);
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Sets the color of player names and distance text displayed next to the lines. Choose a color that's easy to read against the game environment.");
                    ImGui.BulletText("White or light colors work well for maximum readability");
                    ImGui.BulletText("Consider adding a slight transparency for less visual impact");
                    ImGui.BulletText("Text color should contrast with your line color for better visibility");
                    ImGui.Spacing();

                    ImGui.TextWrapped("TIP: You can use these appearance settings in combination with role-based coloring and target highlighting for a comprehensive visual system. The base appearance settings will be used when no special conditions apply.");

                    ImGui.EndTabItem();
                }

                // Filters Tab
                if (ImGui.BeginTabItem("Filters"))
                {
                    ImGui.Spacing();
                    ImGui.TextColored(new(0.7f, 1, 0.7f, 1), "Line Drawing Filters");
                    ImGui.Separator();
                    ImGui.Spacing();

                    ImGui.TextWrapped("The Filters tab allows you to control which players have lines drawn to them and the maximum distance for line drawing. These settings help you focus on specific targets and reduce visual clutter.");
                    ImGui.TextWrapped("In Crystalline Conflict, strategic targeting is crucial. Use these filters to focus on enemies, allies, or all players based on your current role and strategy.");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Select which players to draw lines to:");
                    ImGui.Spacing();

                    // Draw to all players
                    var drawToAllPlayers = Configuration.DrawToAllPlayers;
                    if (ImGui.Checkbox("Draw to All Players", ref drawToAllPlayers))
                    {
                        Configuration.DrawToAllPlayers = drawToAllPlayers;
                        if (drawToAllPlayers)
                        {
                            Configuration.DrawToEnemiesOnly = false;
                            Configuration.DrawToAlliesOnly = false;
                        }
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Draws lines to all players in the area, both allies and enemies. Useful for maintaining awareness of everyone's position but can be visually cluttered in busy areas.");
                    ImGui.TextWrapped("Best used when: You need to track all players, such as when playing a tank or healer role that needs to monitor the entire battlefield.");

                    // Draw to enemies only
                    var drawToEnemiesOnly = Configuration.DrawToEnemiesOnly;
                    if (ImGui.Checkbox("Draw to Enemies Only", ref drawToEnemiesOnly))
                    {
                        Configuration.DrawToEnemiesOnly = drawToEnemiesOnly;
                        if (drawToEnemiesOnly)
                        {
                            Configuration.DrawToAllPlayers = false;
                            Configuration.DrawToAlliesOnly = false;
                        }
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Draws lines only to enemy players. This helps you focus on opponents while reducing visual clutter from ally lines.");
                    ImGui.TextWrapped("Best used when: Playing DPS roles focused on eliminating enemies, or when you need to track enemy positions without distraction.");

                    // Draw to allies only
                    var drawToAlliesOnly = Configuration.DrawToAlliesOnly;
                    if (ImGui.Checkbox("Draw to Allies Only", ref drawToAlliesOnly))
                    {
                        Configuration.DrawToAlliesOnly = drawToAlliesOnly;
                        if (drawToAlliesOnly)
                        {
                            Configuration.DrawToAllPlayers = false;
                            Configuration.DrawToEnemiesOnly = false;
                        }
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Draws lines only to allied players (your party members). This helps you maintain awareness of your team's positions without enemy distractions.");
                    ImGui.TextWrapped("Best used when: Playing support roles like healers, or when coordinating team movements and positioning.");

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Max draw distance
                    var maxDrawDistance = Configuration.MaxDrawDistance;
                    if (ImGui.SliderFloat("Max Draw Distance", ref maxDrawDistance, 10.0f, 100.0f, "%.1f yalms"))
                    {
                        Configuration.MaxDrawDistance = maxDrawDistance;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Sets the maximum distance (in yalms) at which lines will be drawn to players. Players beyond this distance will not have lines drawn to them.");
                    ImGui.TextWrapped("This setting helps reduce visual clutter and focuses your attention on nearby players who are more immediately relevant to combat.");
                    ImGui.BulletText("10-25 yalms: Focus on close-range combat and immediate threats");
                    ImGui.BulletText("30-50 yalms: Balanced visibility for mid-range awareness (recommended)");
                    ImGui.BulletText("60-100 yalms: Maximum awareness but may cause visual clutter");

                    ImGui.Spacing();
                    ImGui.TextWrapped("TIP: In Crystalline Conflict, most combat happens within 30 yalms. Setting your max distance to 30-40 yalms provides good awareness while reducing clutter.");

                    ImGui.EndTabItem();
                }

                // Display Options Tab
                if (ImGui.BeginTabItem("Display Options"))
                {
                    ImGui.Spacing();
                    ImGui.TextColored(new(0.7f, 1, 0.7f, 1), "Display Options");
                    ImGui.Separator();
                    ImGui.Spacing();

                    ImGui.TextWrapped("The Display Options tab controls how information is presented in the game world. These settings affect text visibility, debug information, and window behavior.");
                    ImGui.TextWrapped("Customize these options to balance information visibility with minimal UI clutter for optimal gameplay experience.");
                    ImGui.Spacing();

                    // Show distance option
                    var showDistance = Configuration.ShowDistance;
                    if (ImGui.Checkbox("Show Distance in Yalms", ref showDistance))
                    {
                        Configuration.ShowDistance = showDistance;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When enabled, the distance to each player will be shown in yalms next to their name. This provides precise distance information for better positioning and ability usage.");
                    ImGui.TextWrapped("Distance information is crucial for:");
                    ImGui.BulletText("Maintaining optimal combat range for your job/role");
                    ImGui.BulletText("Knowing when enemies are within range of their abilities");
                    ImGui.BulletText("Positioning yourself correctly for AoE abilities and heals");
                    ImGui.BulletText("Judging when to engage or disengage from combat");

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Overlay text scale
                    ImGui.TextColored(new(0.7f, 1, 0.7f, 1), "Overlay Text Size");
                    ImGui.Spacing();

                    var overlayTextScale = Configuration.OverlayTextScale;
                    if (ImGui.SliderFloat("Text Size", ref overlayTextScale, 0.5f, 1.5f, "%.2f"))
                    {
                        Configuration.OverlayTextScale = overlayTextScale;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Adjust the size of text displayed over players in the game world. This affects player names, distance information, and all other overlay text.");
                    ImGui.TextWrapped("Finding the right text size is important for readability without obscuring gameplay:");
                    ImGui.BulletText("0.5-0.6: Very small text, minimal visual impact but may be hard to read");
                    ImGui.BulletText("0.7-0.8: Recommended for most users - readable but not intrusive");
                    ImGui.BulletText("0.9-1.0: Default size, good readability in most situations");
                    ImGui.BulletText("1.1-1.5: Large text, maximum readability but may obscure game elements");
                    ImGui.TextWrapped("TIP: If you're playing at higher resolutions (1440p or 4K), you may need to use larger text sizes for comfortable reading.");

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Debug window toggle
                    var showDebugWindow = Configuration.ShowDebugWindow;
                    if (ImGui.Checkbox("Show Debug Window", ref showDebugWindow))
                    {
                        Configuration.ShowDebugWindow = showDebugWindow;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When enabled, a debug window will be shown with detailed information about shared targets and player tracking.");
                    ImGui.TextWrapped("The debug window provides valuable insights into how the plugin is working:");
                    ImGui.BulletText("Current target information and health status");
                    ImGui.BulletText("List of all shared targets being tracked and their attackers");
                    ImGui.BulletText("Party member information including roles and targets");
                    ImGui.BulletText("Real-time health tracking and attack detection data");

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Window settings
                    var movable = Configuration.IsConfigWindowMovable;
                    if (ImGui.Checkbox("Movable Config Window", ref movable))
                    {
                        Configuration.IsConfigWindowMovable = movable;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When enabled, you can drag and reposition this configuration window. When disabled, the window will stay fixed in place.");
                    ImGui.TextWrapped("This is useful if you want to prevent accidental movement of the window while making configuration changes.");
                    ImGui.TextWrapped("TIP: Enable this option when you want to position the window, then disable it once you've found the perfect spot.");

                    ImGui.EndTabItem();
                }

                // UI Customization Tab
                if (ImGui.BeginTabItem("UI Customization"))
                {
                    ImGui.Spacing();
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "UI Customization");
                    ImGui.Separator();
                    ImGui.Spacing();

                    ImGui.TextWrapped("The UI Customization tab allows you to personalize the appearance and behavior of the plugin's user interface. You can change colors, adjust scaling, and set window positions.");
                    ImGui.TextWrapped("These settings affect the plugin windows and menus, not the in-game line drawing. For line appearance settings, use the Appearance tab.");
                    ImGui.Spacing();

                    // Theme selection
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Theme Selection");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Choose from predefined color themes or create your own custom theme. The theme affects the colors of all UI elements in the plugin windows.");
                    ImGui.TextWrapped("Each theme provides a cohesive color scheme designed for different preferences and visibility needs:");
                    ImGui.BulletText("Default: A balanced theme with cyan accents on a dark background");
                    ImGui.BulletText("Blue: Cool blue tones that match FFXIV's default UI colors");
                    ImGui.BulletText("Red: Vibrant red accents for high contrast and visibility");
                    ImGui.BulletText("Green: Soothing green tones that are easy on the eyes");
                    ImGui.BulletText("Purple: Rich purple accents for a unique look");
                    ImGui.BulletText("Custom: Create your own color scheme with full control");
                    ImGui.Spacing();

                    string[] themeNames = Enum.GetNames(typeof(UiTheme));
                    int currentThemeIndex = (int)Configuration.SelectedTheme;

                    if (ImGui.BeginCombo("Theme", themeNames[currentThemeIndex]))
                    {
                        for (int i = 0; i < themeNames.Length; i++)
                        {
                            bool isSelected = (currentThemeIndex == i);
                            if (ImGui.Selectable(themeNames[i], isSelected))
                            {
                                Configuration.SelectedTheme = (UiTheme)i;
                                Configuration.ApplyThemeColors();
                                Configuration.Save();
                            }

                            if (isSelected)
                                ImGui.SetItemDefaultFocus();
                        }
                        ImGui.EndCombo();
                    }

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Custom colors (only shown if Custom theme is selected)
                    if (Configuration.SelectedTheme == UiTheme.Custom)
                    {
                        ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Custom Colors");
                        ImGui.Spacing();

                        ImGui.TextWrapped("Customize individual colors for different UI elements. Use the color pickers to select your preferred colors. Changes are applied immediately.");
                        ImGui.TextWrapped("The alpha channel controls transparency (0 = fully transparent, 1 = fully opaque).");
                        ImGui.Spacing();

                        ImGui.TextWrapped("Each color serves a specific purpose in the UI:");
                        ImGui.BulletText("Background Color: The main background color for all plugin windows");
                        ImGui.BulletText("Text Color: The color of regular text throughout the interface");
                        ImGui.BulletText("Header Color: Used for section titles and important labels");
                        ImGui.BulletText("Accent Color: Used for highlights, buttons, and interactive elements");
                        ImGui.TextWrapped("TIP: For best results, ensure there's enough contrast between your background and text colors for readability.");
                        ImGui.Spacing();

                        // Background color
                        var customBgColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiBackgroundColor);
                        if (ImGui.ColorEdit4("Background Color", ref customBgColor, ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar))
                        {
                            // Convert the Vector4 color to bytes and use our helper method
                            byte r = (byte)(customBgColor.X * 255.0f);
                            byte g = (byte)(customBgColor.Y * 255.0f);
                            byte b = (byte)(customBgColor.Z * 255.0f);
                            byte a = (byte)(customBgColor.W * 255.0f);
                            Configuration.UiBackgroundColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }
                        ImGui.Spacing();

                        // Text color
                        var txtColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiTextColor);
                        if (ImGui.ColorEdit4("Text Color", ref txtColor, ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar))
                        {
                            byte r = (byte)(txtColor.X * 255.0f);
                            byte g = (byte)(txtColor.Y * 255.0f);
                            byte b = (byte)(txtColor.Z * 255.0f);
                            byte a = (byte)(txtColor.W * 255.0f);
                            Configuration.UiTextColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }
                        ImGui.Spacing();

                        // Header color
                        var customHeaderColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiHeaderColor);
                        if (ImGui.ColorEdit4("Header Color", ref customHeaderColor, ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar))
                        {
                            byte r = (byte)(customHeaderColor.X * 255.0f);
                            byte g = (byte)(customHeaderColor.Y * 255.0f);
                            byte b = (byte)(customHeaderColor.Z * 255.0f);
                            byte a = (byte)(customHeaderColor.W * 255.0f);
                            Configuration.UiHeaderColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }
                        ImGui.Spacing();

                        // Accent color
                        var accentColor = ImGui.ColorConvertU32ToFloat4(Configuration.UiAccentColor);
                        if (ImGui.ColorEdit4("Accent Color", ref accentColor, ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar))
                        {
                            byte r = (byte)(accentColor.X * 255.0f);
                            byte g = (byte)(accentColor.Y * 255.0f);
                            byte b = (byte)(accentColor.Z * 255.0f);
                            byte a = (byte)(accentColor.W * 255.0f);
                            Configuration.UiAccentColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }

                        ImGui.Spacing();
                        ImGui.Separator();
                        ImGui.Spacing();
                    }

                    // UI Scaling
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "UI Scaling");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Adjust the size of all UI elements to improve readability or fit your screen better. This scaling affects all text, buttons, and interactive elements in the plugin windows.");
                    ImGui.TextWrapped("UI scaling is particularly important for different monitor resolutions and accessibility needs:");
                    ImGui.BulletText("Lower values (0.5-0.8): Compact UI that takes up less screen space");
                    ImGui.BulletText("Default value (1.0): Standard size that works well for most displays");
                    ImGui.BulletText("Higher values (1.2-2.0): Larger UI elements for better readability");
                    ImGui.TextWrapped("Note that this setting is separate from the Overlay Text Scale in the Display Options tab, which affects text displayed in the game world.");
                    ImGui.Spacing();

                    var uiScale = Configuration.UiScale;
                    if (ImGui.SliderFloat("UI Scale", ref uiScale, 0.5f, 2.0f, "%.2f"))
                    {
                        Configuration.UiScale = uiScale;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("Changes to UI scale will take effect after restarting the plugin.");
                    ImGui.TextWrapped("Recommended values: 0.8 for high-DPI displays, 1.0 for standard displays, 1.2-1.5 for improved readability.");

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Window Position
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Window Position");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Configure the position of the main window on your screen. This allows you to place the plugin window in a consistent location that works best with your UI layout.");
                    ImGui.TextWrapped("Window positioning features:");
                    ImGui.BulletText("Lock Position: Prevents the window from being moved accidentally");
                    ImGui.BulletText("Save Current Position: Stores the current window location");
                    ImGui.BulletText("Reset Position: Returns the window to the default location");
                    ImGui.TextWrapped("TIP: Position the window where it won't overlap with important game UI elements but is still easily accessible when needed.");
                    ImGui.Spacing();

                    var lockPosition = Configuration.LockMainWindowPosition;
                    if (ImGui.Checkbox("Lock Main Window Position", ref lockPosition))
                    {
                        Configuration.LockMainWindowPosition = lockPosition;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When locked, the main window will always appear at the saved position when opened.");

                    if (Configuration.LockMainWindowPosition)
                    {
                        ImGui.Spacing();
                        ImGui.TextUnformatted("Current Position: " +
                                           $"X: {Configuration.MainWindowPosition.X}, " +
                                           $"Y: {Configuration.MainWindowPosition.Y}");

                        if (ImGui.Button("Save Current Position"))
                        {
                            // Get the current window position
                            Configuration.MainWindowPosition = ImGui.GetWindowPos();
                            Configuration.Save();
                        }

                        ImGui.SameLine();

                        if (ImGui.Button("Reset Position"))
                        {
                            Configuration.MainWindowPosition = new Vector2(100, 100);
                            Configuration.Save();
                        }
                    }

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    ImGui.EndTabItem();
                }



                // Target Filtering Tab
                if (ImGui.BeginTabItem("Target Filtering"))
                {
                    ImGui.Spacing();
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Target Filtering and Prioritization");
                    ImGui.Separator();
                    ImGui.Spacing();

                    ImGui.TextWrapped("The Target Filtering tab provides advanced options for highlighting specific targets, prioritizing enemies, and enhancing team coordination in Crystalline Conflict.");
                    ImGui.TextWrapped("These features help you:");
                    ImGui.BulletText("Quickly identify high-value targets based on role");
                    ImGui.BulletText("Coordinate attacks with your team on the same target");
                    ImGui.BulletText("Spot enemies that are close to being defeated");
                    ImGui.BulletText("Maintain awareness of which enemies your team is focusing");
                    ImGui.BulletText("Identify targets based on their role");
                    ImGui.Spacing();

                    // Role Filtering Section
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Role Filtering");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Filter targets by their role and assign different colors to each role for better visibility. This helps you quickly identify high-value targets based on their role in the enemy team.");
                    ImGui.TextWrapped("Role-based targeting is crucial in Crystalline Conflict because different roles require different approaches:");
                    ImGui.BulletText("Tanks: Durable targets that may be harder to kill but often lead pushes");
                    ImGui.BulletText("Healers: High-value targets that keep the enemy team alive");
                    ImGui.BulletText("DPS: Damage dealers that can be more vulnerable but dangerous");
                    ImGui.TextWrapped("Use role filtering to focus on specific roles based on your team's strategy and your own role.");
                    ImGui.Spacing();

                    var enableTargetFiltering = Configuration.EnableTargetFiltering;
                    if (ImGui.Checkbox("Enable Role Filtering", ref enableTargetFiltering))
                    {
                        Configuration.EnableTargetFiltering = enableTargetFiltering;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When enabled, lines will be colored based on the target's role (tank, healer, or DPS).");
                    ImGui.Spacing();

                    if (Configuration.EnableTargetFiltering)
                    {
                        // Role checkboxes
                        var filterTanks = Configuration.FilterTanks;
                        if (ImGui.Checkbox("Show Tanks", ref filterTanks))
                        {
                            Configuration.FilterTanks = filterTanks;
                            Configuration.Save();
                        }
                        ImGui.SameLine(150);
                        var tankColor = ImGui.ColorConvertU32ToFloat4(Configuration.TankColor);
                        if (ImGui.ColorEdit4("Tank Color##TankColor", ref tankColor, ImGuiColorEditFlags.NoInputs | ImGuiColorEditFlags.AlphaPreview))
                        {
                            byte r = (byte)(tankColor.X * 255.0f);
                            byte g = (byte)(tankColor.Y * 255.0f);
                            byte b = (byte)(tankColor.Z * 255.0f);
                            byte a = (byte)(tankColor.W * 255.0f);
                            Configuration.TankColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }

                        var filterHealers = Configuration.FilterHealers;
                        if (ImGui.Checkbox("Show Healers", ref filterHealers))
                        {
                            Configuration.FilterHealers = filterHealers;
                            Configuration.Save();
                        }
                        ImGui.SameLine(150);
                        var healerColor = ImGui.ColorConvertU32ToFloat4(Configuration.HealerColor);
                        if (ImGui.ColorEdit4("Healer Color##HealerColor", ref healerColor, ImGuiColorEditFlags.NoInputs | ImGuiColorEditFlags.AlphaPreview))
                        {
                            byte r = (byte)(healerColor.X * 255.0f);
                            byte g = (byte)(healerColor.Y * 255.0f);
                            byte b = (byte)(healerColor.Z * 255.0f);
                            byte a = (byte)(healerColor.W * 255.0f);
                            Configuration.HealerColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }

                        var filterDPS = Configuration.FilterDPS;
                        if (ImGui.Checkbox("Show DPS", ref filterDPS))
                        {
                            Configuration.FilterDPS = filterDPS;
                            Configuration.Save();
                        }
                        ImGui.SameLine(150);
                        var dpsColor = ImGui.ColorConvertU32ToFloat4(Configuration.DPSColor);
                        if (ImGui.ColorEdit4("DPS Color##DPSColor", ref dpsColor, ImGuiColorEditFlags.NoInputs | ImGuiColorEditFlags.AlphaPreview))
                        {
                            byte r = (byte)(dpsColor.X * 255.0f);
                            byte g = (byte)(dpsColor.Y * 255.0f);
                            byte b = (byte)(dpsColor.Z * 255.0f);
                            byte a = (byte)(dpsColor.W * 255.0f);
                            Configuration.DPSColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }
                    }

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Kill Potential Indicators Section
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Kill Potential Indicators");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Highlight enemies that are close to being defeated with special visual indicators. This feature helps you quickly identify and focus on enemies that can be eliminated, creating numerical advantages for your team.");
                    ImGui.TextWrapped("Kill Potential Indicators provide crucial tactical information:");
                    ImGui.BulletText("Instantly identify enemies below the kill threshold");
                    ImGui.BulletText("Focus your burst damage on targets that can be eliminated quickly");
                    ImGui.BulletText("Make better decisions about when to commit offensive cooldowns");
                    ImGui.BulletText("Coordinate with your team to secure kills efficiently");
                    ImGui.BulletText("Create numerical advantages that can turn the tide of battle");
                    ImGui.TextWrapped("TIP: In Crystalline Conflict, securing kills is one of the most important factors for winning matches. Use Kill Potential Indicators to identify the best opportunities for eliminating enemies and creating advantageous situations for your team.");
                    ImGui.Spacing();

                    var enableKillPotentialIndicators = Configuration.EnableKillPotentialIndicators;
                    if (ImGui.Checkbox("Enable Kill Potential Indicators", ref enableKillPotentialIndicators))
                    {
                        Configuration.EnableKillPotentialIndicators = enableKillPotentialIndicators;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When enabled, enemies below the kill threshold will be highlighted with special visual indicators.");
                    ImGui.Spacing();

                    if (Configuration.EnableKillPotentialIndicators)
                    {
                        // Kill threshold slider
                        float killThreshold = Configuration.KillThreshold;
                        if (ImGui.SliderFloat("Kill Threshold", ref killThreshold, 0.05f, 0.5f, "%.2f"))
                        {
                            Configuration.KillThreshold = killThreshold;
                            Configuration.Save();
                        }
                        ImGui.TextWrapped($"Targets below {(int)(killThreshold * 100)}% health will be marked as potential kills.");
                        ImGui.Spacing();

                        // Kill target color
                        var killTargetColor = ImGui.ColorConvertU32ToFloat4(Configuration.KillTargetColor);
                        if (ImGui.ColorEdit4("Kill Target Color", ref killTargetColor, ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar))
                        {
                            byte r = (byte)(killTargetColor.X * 255.0f);
                            byte g = (byte)(killTargetColor.Y * 255.0f);
                            byte b = (byte)(killTargetColor.Z * 255.0f);
                            byte a = (byte)(killTargetColor.W * 255.0f);
                            Configuration.KillTargetColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }

                        // Kill target line thickness
                        float killTargetLineThickness = Configuration.KillTargetLineThickness;
                        if (ImGui.SliderFloat("Kill Target Line Thickness", ref killTargetLineThickness, 1.0f, 10.0f, "%.1f"))
                        {
                            Configuration.KillTargetLineThickness = killTargetLineThickness;
                            Configuration.Save();
                        }

                        // Pulse effect
                        var pulseKillTargetLine = Configuration.PulseKillTargetLine;
                        if (ImGui.Checkbox("Pulse Kill Target Line", ref pulseKillTargetLine))
                        {
                            Configuration.PulseKillTargetLine = pulseKillTargetLine;
                            Configuration.Save();
                        }
                        ImGui.TextWrapped("When enabled, the kill target line will pulse to make it more noticeable.");
                        ImGui.Spacing();

                        // Kill icon options
                        var showKillIcon = Configuration.ShowKillIcon;
                        if (ImGui.Checkbox("Show Kill Icon", ref showKillIcon))
                        {
                            Configuration.ShowKillIcon = showKillIcon;
                            Configuration.Save();
                        }
                        ImGui.TextWrapped("When enabled, a special icon will be displayed on potential kill targets.");
                        ImGui.Spacing();

                        if (Configuration.ShowKillIcon)
                        {
                            // Kill icon symbol
                            string killIconSymbol = Configuration.KillIconSymbol;
                            if (ImGui.BeginCombo("Kill Icon Symbol", killIconSymbol))
                            {
                                string[] symbols = { "×", "✗", "†", "☠", "⚔", "!", "▼", "★" };
                                for (int i = 0; i < symbols.Length; i++)
                                {
                                    bool isSelected = (killIconSymbol == symbols[i]);
                                    if (ImGui.Selectable(symbols[i], isSelected))
                                    {
                                        Configuration.KillIconSymbol = symbols[i];
                                        Configuration.Save();
                                    }

                                    if (isSelected)
                                        ImGui.SetItemDefaultFocus();
                                }
                                ImGui.EndCombo();
                            }

                            // Kill icon scale
                            float killIconScale = Configuration.KillIconScale;
                            if (ImGui.SliderFloat("Kill Icon Scale", ref killIconScale, 1.0f, 5.0f, "%.1f"))
                            {
                                Configuration.KillIconScale = killIconScale;
                                Configuration.Save();
                            }
                        }
                    }

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Focus Target Tracking Section
                    ImGui.TextColored(new Vector4(0.7f, 1, 0.7f, 1), "Focus Target Tracking");
                    ImGui.Spacing();

                    ImGui.TextWrapped("Highlight your focus target with special visual indicators. This feature helps you keep track of specific players you want to monitor closely during combat.");
                    ImGui.TextWrapped("Focus Target Tracking provides several tactical advantages:");
                    ImGui.BulletText("Maintain awareness of important targets even when targeting someone else");
                    ImGui.BulletText("Track high-priority enemies like healers or burst damage dealers");
                    ImGui.BulletText("Monitor specific players who pose the greatest threat to your team");
                    ImGui.BulletText("Coordinate with your team by focusing the same target");
                    ImGui.TextWrapped("TIP: In Crystalline Conflict, setting a focus target on key enemies like healers can help you track their position even when you're targeting and attacking someone else.");
                    ImGui.Spacing();

                    var enableFocusTargetTracking = Configuration.EnableFocusTargetTracking;
                    if (ImGui.Checkbox("Enable Focus Target Tracking", ref enableFocusTargetTracking))
                    {
                        Configuration.EnableFocusTargetTracking = enableFocusTargetTracking;
                        Configuration.Save();
                    }
                    ImGui.TextWrapped("When enabled, your focus target will be highlighted with special visual indicators.");
                    ImGui.Spacing();

                    if (Configuration.EnableFocusTargetTracking)
                    {
                        // Focus target color
                        var focusTargetColor = ImGui.ColorConvertU32ToFloat4(Configuration.FocusTargetColor);
                        if (ImGui.ColorEdit4("Focus Target Color", ref focusTargetColor, ImGuiColorEditFlags.AlphaPreview | ImGuiColorEditFlags.AlphaBar))
                        {
                            byte r = (byte)(focusTargetColor.X * 255.0f);
                            byte g = (byte)(focusTargetColor.Y * 255.0f);
                            byte b = (byte)(focusTargetColor.Z * 255.0f);
                            byte a = (byte)(focusTargetColor.W * 255.0f);
                            Configuration.FocusTargetColor = Configuration.ImGuiColorToUint(r, g, b, a);
                            Configuration.Save();
                        }

                        // Focus target line thickness
                        float focusTargetLineThickness = Configuration.FocusTargetLineThickness;
                        if (ImGui.SliderFloat("Focus Target Line Thickness", ref focusTargetLineThickness, 1.0f, 10.0f, "%.1f"))
                        {
                            Configuration.FocusTargetLineThickness = focusTargetLineThickness;
                            Configuration.Save();
                        }

                        // Pulse effect
                        var pulseFocusTargetLine = Configuration.PulseFocusTargetLine;
                        if (ImGui.Checkbox("Pulse Focus Target Line", ref pulseFocusTargetLine))
                        {
                            Configuration.PulseFocusTargetLine = pulseFocusTargetLine;
                            Configuration.Save();
                        }
                        ImGui.TextWrapped("When enabled, the focus target line will pulse to make it more noticeable.");
                        ImGui.Spacing();

                        // Focus icon options
                        var showFocusTargetIcon = Configuration.ShowFocusTargetIcon;
                        if (ImGui.Checkbox("Show Focus Target Icon", ref showFocusTargetIcon))
                        {
                            Configuration.ShowFocusTargetIcon = showFocusTargetIcon;
                            Configuration.Save();
                        }
                        ImGui.TextWrapped("When enabled, a special icon will be displayed on your focus target.");
                        ImGui.Spacing();

                        if (Configuration.ShowFocusTargetIcon)
                        {
                            // Focus icon symbol
                            string focusTargetIconSymbol = Configuration.FocusTargetIconSymbol;
                            if (ImGui.BeginCombo("Focus Target Icon Symbol", focusTargetIconSymbol))
                            {
                                string[] symbols = { "★", "☆", "◎", "◉", "⊕", "⊙", "⚡", "❖" };
                                for (int i = 0; i < symbols.Length; i++)
                                {
                                    bool isSelected = (focusTargetIconSymbol == symbols[i]);
                                    if (ImGui.Selectable(symbols[i], isSelected))
                                    {
                                        Configuration.FocusTargetIconSymbol = symbols[i];
                                        Configuration.Save();
                                    }

                                    if (isSelected)
                                        ImGui.SetItemDefaultFocus();
                                }
                                ImGui.EndCombo();
                            }

                            // Focus icon scale
                            float focusTargetIconScale = Configuration.FocusTargetIconScale;
                            if (ImGui.SliderFloat("Focus Target Icon Scale", ref focusTargetIconScale, 1.0f, 5.0f, "%.1f"))
                            {
                                Configuration.FocusTargetIconScale = focusTargetIconScale;
                                Configuration.Save();
                            }
                        }
                    }

                    ImGui.Spacing();
                    ImGui.Separator();
                    ImGui.Spacing();

                    // Removed Enemy Targeting Me Section

                    ImGui.EndTabItem();
                }



                ImGui.EndTabBar();
            }
        }
        else
        {
            ImGui.Spacing();
            ImGui.TextColored(new Vector4(1, 0.5f, 0.5f, 1), "Line drawing is currently disabled.");
            ImGui.TextWrapped("Enable the checkbox above to access all settings.");
        }

        ImGui.PopStyleVar(); // Pop the item spacing

        // Pop style colors and font scaling
        ImGui.PopFont();
        ImGui.PopStyleColor(2); // Pop the two style colors we pushed at the beginning
    }
}
