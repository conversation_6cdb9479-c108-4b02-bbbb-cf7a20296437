using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using CCLineDrawer.Services;

namespace CCLineDrawer.Tests
{
    /// <summary>
    /// Simple testing framework for the plugin
    /// </summary>
    public class TestFramework
    {
        private readonly List<TestCase> _testCases = new();
        private readonly LoggingService _logger;

        public TestFramework(LoggingService logger)
        {
            _logger = logger;
        }

        public void AddTest(string name, Action testAction, string description = "")
        {
            _testCases.Add(new TestCase(name, testAction, description));
        }

        public TestResults RunAllTests()
        {
            var results = new TestResults();
            var stopwatch = Stopwatch.StartNew();

            _logger.LogInfo($"Running {_testCases.Count} tests...");

            foreach (var testCase in _testCases)
            {
                var testResult = RunSingleTest(testCase);
                results.AddResult(testResult);
            }

            stopwatch.Stop();
            results.TotalExecutionTime = stopwatch.Elapsed;

            LogResults(results);
            return results;
        }

        private TestResult RunSingleTest(TestCase testCase)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestResult(testCase.Name, testCase.Description);

            try
            {
                testCase.TestAction();
                result.Status = TestStatus.Passed;
                _logger.LogDebug($"✓ {testCase.Name} passed");
            }
            catch (AssertionException ex)
            {
                result.Status = TestStatus.Failed;
                result.ErrorMessage = ex.Message;
                _logger.LogWarning($"✗ {testCase.Name} failed: {ex.Message}");
            }
            catch (Exception ex)
            {
                result.Status = TestStatus.Error;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, $"✗ {testCase.Name} error");
            }

            stopwatch.Stop();
            result.ExecutionTime = stopwatch.Elapsed;
            return result;
        }

        private void LogResults(TestResults results)
        {
            _logger.LogInfo("=== Test Results ===");
            _logger.LogInfo($"Total: {results.TotalTests}");
            _logger.LogInfo($"Passed: {results.PassedTests}");
            _logger.LogInfo($"Failed: {results.FailedTests}");
            _logger.LogInfo($"Errors: {results.ErrorTests}");
            _logger.LogInfo($"Success Rate: {results.SuccessRate:P1}");
            _logger.LogInfo($"Total Time: {results.TotalExecutionTime.TotalMilliseconds:F0}ms");
        }
    }

    public class TestCase
    {
        public string Name { get; }
        public Action TestAction { get; }
        public string Description { get; }

        public TestCase(string name, Action testAction, string description = "")
        {
            Name = name;
            TestAction = testAction;
            Description = description;
        }
    }

    public class TestResult
    {
        public string Name { get; }
        public string Description { get; }
        public TestStatus Status { get; set; }
        public string ErrorMessage { get; set; } = "";
        public TimeSpan ExecutionTime { get; set; }

        public TestResult(string name, string description = "")
        {
            Name = name;
            Description = description;
        }
    }

    public class TestResults
    {
        private readonly List<TestResult> _results = new();

        public int TotalTests => _results.Count;
        public int PassedTests => _results.Count(r => r.Status == TestStatus.Passed);
        public int FailedTests => _results.Count(r => r.Status == TestStatus.Failed);
        public int ErrorTests => _results.Count(r => r.Status == TestStatus.Error);
        public double SuccessRate => TotalTests > 0 ? (double)PassedTests / TotalTests : 0;
        public TimeSpan TotalExecutionTime { get; set; }

        public void AddResult(TestResult result)
        {
            _results.Add(result);
        }

        public IReadOnlyList<TestResult> GetResults() => _results.AsReadOnly();
    }

    public enum TestStatus
    {
        Passed,
        Failed,
        Error
    }

    public class AssertionException : Exception
    {
        public AssertionException(string message) : base(message) { }
    }

    /// <summary>
    /// Simple assertion methods for testing
    /// </summary>
    public static class Assert
    {
        public static void IsTrue(bool condition, string message = "Expected true but was false")
        {
            if (!condition)
                throw new AssertionException(message);
        }

        public static void IsFalse(bool condition, string message = "Expected false but was true")
        {
            if (condition)
                throw new AssertionException(message);
        }

        public static void AreEqual<T>(T expected, T actual, string message = "")
        {
            if (!Equals(expected, actual))
            {
                var msg = string.IsNullOrEmpty(message) 
                    ? $"Expected {expected} but was {actual}"
                    : $"{message}. Expected {expected} but was {actual}";
                throw new AssertionException(msg);
            }
        }

        public static void IsNotNull<T>(T value, string message = "Expected non-null value")
        {
            if (value == null)
                throw new AssertionException(message);
        }

        public static void IsNull<T>(T value, string message = "Expected null value")
        {
            if (value != null)
                throw new AssertionException(message);
        }

        public static void Throws<TException>(Action action, string message = "") where TException : Exception
        {
            try
            {
                action();
                var msg = string.IsNullOrEmpty(message)
                    ? $"Expected {typeof(TException).Name} to be thrown"
                    : message;
                throw new AssertionException(msg);
            }
            catch (TException)
            {
                // Expected exception was thrown
            }
            catch (Exception ex)
            {
                throw new AssertionException($"Expected {typeof(TException).Name} but got {ex.GetType().Name}");
            }
        }
    }
}
