using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.Types;
using Dalamud.Game.ClientState.Objects.Enums;
using Dalamud.Plugin.Services;

namespace CCLineDrawer.Services
{
    /// <summary>
    /// Service responsible for tracking and caching player information
    /// </summary>
    public class PlayerTrackingService
    {
        private readonly IObjectTable _objectTable;
        private readonly IClientState _clientState;
        private readonly IPluginLog _log;
        
        // Caches for performance optimization
        private readonly List<ICharacter> _playerCache = new(40);
        private readonly List<ICharacter> _partyMemberCache = new(8);
        private DateTime _lastPlayerCacheUpdate = DateTime.MinValue;
        private DateTime _lastPartyMemberCacheUpdate = DateTime.MinValue;
        
        private const int PLAYER_CACHE_UPDATE_INTERVAL_MS = 100;
        private const int PARTY_CACHE_UPDATE_INTERVAL_MS = 500;

        public PlayerTrackingService(IObjectTable objectTable, IClientState clientState, IPluginLog log)
        {
            _objectTable = objectTable;
            _clientState = clientState;
            _log = log;
        }

        public IReadOnlyList<ICharacter> GetCachedPlayers()
        {
            UpdatePlayerCacheIfNeeded();
            return _playerCache.AsReadOnly();
        }

        public IReadOnlyList<ICharacter> GetCachedPartyMembers()
        {
            UpdatePartyCacheIfNeeded();
            return _partyMemberCache.AsReadOnly();
        }

        private void UpdatePlayerCacheIfNeeded()
        {
            bool shouldUpdate = (DateTime.Now - _lastPlayerCacheUpdate).TotalMilliseconds >= PLAYER_CACHE_UPDATE_INTERVAL_MS;
            
            if (!shouldUpdate) return;

            var localPlayer = _clientState.LocalPlayer;
            if (localPlayer == null) return;

            _playerCache.Clear();

            foreach (var obj in _objectTable)
            {
                if (obj.ObjectKind == ObjectKind.Player &&
                    obj.Address != localPlayer.Address &&
                    obj is ICharacter character &&
                    character.CurrentHp > 0)
                {
                    _playerCache.Add(character);
                }
            }

            _lastPlayerCacheUpdate = DateTime.Now;
            _log.Debug($"Updated player cache with {_playerCache.Count} players");
        }

        private void UpdatePartyCacheIfNeeded()
        {
            bool shouldUpdate = (DateTime.Now - _lastPartyMemberCacheUpdate).TotalMilliseconds >= PARTY_CACHE_UPDATE_INTERVAL_MS;
            
            if (!shouldUpdate) return;

            _partyMemberCache.Clear();

            foreach (var obj in _objectTable)
            {
                if (obj.ObjectKind == ObjectKind.Player && obj is ICharacter character)
                {
                    _partyMemberCache.Add(character);
                }
            }

            _lastPartyMemberCacheUpdate = DateTime.Now;
            _log.Debug($"Updated party cache with {_partyMemberCache.Count} members");
        }

        public static float GetPlayerHealthPercentage(ICharacter player)
        {
            if (player?.MaxHp == 0) return 0f;
            return (float)player.CurrentHp / player.MaxHp;
        }

        public static bool IsCharacterFacingTarget(ICharacter source, ICharacter target)
        {
            try
            {
                Vector3 directionToTarget = Vector3.Normalize(target.Position - source.Position);
                float angle = source.Rotation;
                Vector3 forwardDirection = new((float)Math.Sin(angle), 0, (float)Math.Cos(angle));
                float dotProduct = Vector3.Dot(forwardDirection, directionToTarget);
                return dotProduct > 0.7f; // Within 45 degrees
            }
            catch
            {
                return false;
            }
        }
    }
}
